use serde::{Serialize, Deserialize};
use serde::de::DeserializeOwned;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use surrealdb::Surreal;
use surrealdb::engine::remote::http::{Client, Http};
use surrealdb::opt::auth::Root;
use uuid::Uuid;
use std::collections::HashMap; // Import HashMap
use serde_json::json;

// Import only the PrismaResult from generics and the DomainError for our error handling
use crate::err::types::generics::PrismaResult;
use crate::err::DomainError as DomainPrismaError;
use crate::err::GenericError;

use crate::storage::traits::{DatabaseConnection, DataStore, LongTermMemoryStore};
use crate::storage::types::Record; // Import Record type
use crate::llm::interface::Memory;
use crate::storage::types::thing_id_string; // Add this use statement

// Add custom serialization for datetime to ensure compatibility with SurrealDB
mod datetime_format {
    use chrono::{DateTime, Utc};
    use serde::{self, Deserialize, Deserializer, Serializer};

    pub fn serialize<S>(dt: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // Format as RFC3339 for compatibility with SurrealDB
        serializer.serialize_str(&dt.to_rfc3339())
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        DateTime::parse_from_rfc3339(&s)
            .map(|dt| dt.with_timezone(&Utc))
            .map_err(serde::de::Error::custom)
    }
}

#[derive(Debug)] // Add Debug derive
pub struct SurrealDbConnection {
    db: Surreal<surrealdb::engine::remote::http::Client>,
}

impl SurrealDbConnection {
    /// Creates a mock SurrealDbConnection for testing purposes.
    /// This should only be used in tests.
    pub fn new_mock() -> Self {
        use std::sync::Arc;
        use std::time::Instant;
        use surrealdb::engine::any::Any;
        use surrealdb::opt::Config;

        // Create a memory-only database for testing
        println!("Creating mock SurrealDB connection...");
        let start = Instant::now();

        // Create a minimal configuration that doesn't try to connect to a real database
        let db = Surreal::init();

        // Skip actual initialization to avoid delays
        println!("SurrealDB mock initialized in {:?} (skipped actual connection)", start.elapsed());

        Self { db }
    }
}

#[async_trait]
impl DatabaseConnection for SurrealDbConnection {
    async fn connect(connection_string: &str) -> PrismaResult<Self> {
        // Parse the connection string to extract host and port
        // SurrealDB HTTP client expects "host:port" format, not full URL
        let endpoint = if connection_string.starts_with("http://") {
            connection_string.strip_prefix("http://").unwrap_or(connection_string)
        } else if connection_string.starts_with("ws://") {
            connection_string.strip_prefix("ws://").unwrap_or(connection_string)
        } else {
            connection_string
        };

        println!("Connecting to SurrealDB at endpoint: {}", endpoint);

        let db = Surreal::new::<Http>(endpoint).await
            .map_err(|e| {
                let domain_error = DomainPrismaError::ConnectionError(format!("Failed to connect: {}", e));
                GenericError::from(domain_error)
            })?;
        // TODO: Add signin and use_db logic here based on config passed in, or handle in a separate init method.
        Ok(Self { db })
    }

    async fn disconnect(&self) -> PrismaResult<()> {
        // SurrealDB client doesn't have an explicit disconnect method for HTTP.
        // Dropping the client handles closure.
        Ok(())
    }

    async fn signin<C, R>(&self, credentials: C) -> PrismaResult<R>
    where
        C: surrealdb::opt::auth::Credentials<surrealdb::opt::auth::Signin, R> + Send + Sync,
        R: serde::de::DeserializeOwned + Send + Sync,
    {
        self.db.signin(credentials).await
            .map_err(|e| {
                let domain_error = DomainPrismaError::AuthError(format!("Failed to sign in: {}", e));
                GenericError::from(domain_error)
            })
    }

    async fn use_ns(&self, namespace: &str) -> PrismaResult<()> {
        self.db.use_ns(namespace).await
            .map_err(|e| {
                let domain_error = DomainPrismaError::DatabaseError(format!("Failed to use namespace: {}", e));
                GenericError::from(domain_error)
            })
    }

    async fn use_db(&self, database: &str) -> PrismaResult<()> {
        self.db.use_db(database).await
            .map_err(|e| {
                let domain_error = DomainPrismaError::DatabaseError(format!("Failed to use database: {}", e));
                GenericError::from(domain_error)
            })
    }

    async fn ping(&self) -> PrismaResult<()> {
        // Use the health check endpoint
        self.db.health().await.map_err(|e| {
            let domain_error = DomainPrismaError::ConnectionError(format!("Ping failed: {}", e));
            GenericError::from(domain_error)
        })?;
        Ok(())
    }
}

#[async_trait]
impl DataStore for SurrealDbConnection {
    async fn create<T: Serialize + Send + Sync>(&self, table: &str, data: &T) -> PrismaResult<Record> {
        // Serialize the data to a JSON Value for manipulation
        let data_value = serde_json::to_value(data)
            .map_err(|e| {
                let domain_error = DomainPrismaError::SerializationError(format!("Failed to serialize data: {}", e));
                GenericError::from(domain_error)
            })?;
        
        // Extract the ID if present
        let (id_part, create_data) = if let Some(obj) = data_value.as_object() {
            if let Some(id_value) = obj.get("id") {
                if let Some(id_str) = id_value.as_str() {
                    // Extract the ID part after the table name if it's in the format "table:id"
                    let id_part = id_str.split(':').last().unwrap_or(id_str).to_string();
                    
                    // Create a copy of the object without the ID field to avoid conflicts
                    let mut new_obj = obj.clone();
                    new_obj.remove("id");
                    
                    (Some(id_part), serde_json::Value::Object(new_obj))
                } else {
                    (None, data_value.clone())
                }
            } else {
                (None, data_value.clone())
            }
        } else {
            (None, data_value.clone())
        };

        // Build the SQL INSERT query based on the data
        let id = match id_part {
            Some(id) => id,
            None => uuid::Uuid::new_v4().simple().to_string(), // Generate a UUID if no ID provided
        };
        
        // Convert the data object to a SET clause
        let mut set_parts = Vec::new();
        if let Some(obj) = create_data.as_object() {
            for (key, value) in obj {
                let value_str = serde_json::to_string(value)
                    .map_err(|e| {
                        let domain_error = DomainPrismaError::SerializationError(format!("Failed to serialize field value: {}", e));
                        GenericError::from(domain_error)
                    })?;
                set_parts.push(format!("{} = {}", key, value_str));
            }
        }
        
        // Construct the CREATE query
        let create_query = format!(
            "CREATE {}:{} SET {}",
            table,
            id,
            set_parts.join(", ")
        );
        
        println!("Executing CREATE query: {}", create_query);
        
        // Execute the raw query
        let mut response = self.db.query(&create_query)
            .await
            .map_err(|e| {
                let domain_error = DomainPrismaError::DatabaseError(format!("Create operation failed: {}", e));
                GenericError::from(domain_error)
            })?;
        
        // Check for errors
        response.check()
            .map_err(|e| {
                let domain_error = DomainPrismaError::DatabaseError(format!("Create query execution check failed: {}", e));
                GenericError::from(domain_error)
            })?;
        
        // Return the record with the ID
        let full_id = format!("{}:{}", table, id);
        Ok(Record { id: full_id })
    }

    async fn get<T: DeserializeOwned + Send + Sync>(&self, table: &str, id: &str) -> PrismaResult<Option<T>> {
        // Use SurrealDB's select method for retrieving a single record by ID.
        let result: Option<T> = self.db.select((table, id))
            .await
            .map_err(|e| {
                let domain_error = DomainPrismaError::DatabaseError(format!("Get operation failed for {}/{}: {}", table, id, e));
                GenericError::from(domain_error)
            })?;

        Ok(result)
    }

     async fn update<T: Serialize + Send + Sync>(&self, table: &str, id: &str, data: &T) -> PrismaResult<Record> {
        // Placeholder implementation for update operation
        // Example:
        // let updated: Option<Record> = self.db.update((table, id)).merge(data).await.map_err(...)?;
        // updated.ok_or_else(...)
        Err(GenericError::from(
            DomainPrismaError::NotFoundError("Update operation not implemented".to_string())
        ))
    }

    async fn delete(&self, table: &str, id: &str) -> PrismaResult<bool> {
        // Use SurrealDB's delete method. It returns the deleted record (or None).
        // We deserialize into Record to confirm deletion, leveraging its ID handling.
        let deleted: Option<Record> = self.db.delete((table, id))
            .await
            .map_err(|e| {
                let domain_error = DomainPrismaError::DatabaseError(format!("Delete operation failed for {}/{}: {}", table, id, e));
                GenericError::from(domain_error)
            })?;
        Ok(deleted.is_some()) // Return true if something was deleted
    }

    // Corrected single implementation for query
    async fn query<T: DeserializeOwned + Send + Sync>(&self, query: &str, params: &[(&str, &str)]) -> PrismaResult<Vec<T>> {
        println!("Executing query: {}", query); // Log the query
        // Convert params slice to HashMap<String, Value> for SurrealDB binding
        let bindings: HashMap<String, surrealdb::sql::Value> = params
            .iter()
            .map(|(k, v)| (k.to_string(), surrealdb::sql::Value::from(*v)))
            .collect();

        // Execute the query
        let response = self.db.query(query) // Remove 'mut' as we reassign after check
            .bind(bindings)
            .await
            .map_err(|e| {
                let domain_error = DomainPrismaError::DatabaseError(format!("Query execution failed: {}", e));
                GenericError::from(domain_error)
            })?;
        println!("Query executed, checking response...");

        // Check for SurrealDB execution errors first, reassign response
        let mut response = response.check().map_err(|e| { // Add 'mut' here, reassign checked response
            println!("Query check failed: {}", e); // Log check failure
            let domain_error = DomainPrismaError::DatabaseError(format!("Query execution check failed: {}", e));
            GenericError::from(domain_error)
        })?;
        println!("Query check passed.");

        // Check if it's a likely non-data-returning query *before* trying to take the result
        let query_upper = query.trim_start().to_uppercase();
        println!("Query upper: {}", query_upper); // Log upper-case query
        if query_upper.starts_with("DEFINE") || query_upper.starts_with("DELETE") || query_upper.starts_with("REMOVE") {
            println!("Detected DEFINE/DELETE/REMOVE, returning empty Vec."); // Log detection
            return Ok(Vec::new());
        }

        // For other query types (SELECT, CREATE, UPDATE potentially), attempt to deserialize the resultset
        println!("Attempting response.take(0)... for type {}", std::any::type_name::<T>()); // Log before take
        match response.take(0) {
            Ok(result) => {
                println!("response.take(0) successful."); // Log success
                Ok(result)
            },
            Err(e) => {
                println!("response.take(0) failed: {}", e); // Log failure
                Err(GenericError::from(DomainPrismaError::DatabaseError(format!(
                    "Failed to take/deserialize query result for query '{}': {}",
                    query,
                    e
                ))))
            }
        }
    }

    // Implementation for get_conversation_history
    async fn get_conversation_history<T: DeserializeOwned + Send + Sync>(
        &self,
        agent_id: &str,
        limit: usize,
    ) -> PrismaResult<Vec<T>> {
        // TODO: Ensure the 'chat_message' table and ChatMessage struct have an 'agent_id' field.
        let query = "SELECT * FROM chat_message WHERE agent_id = $agent_id ORDER BY created_at DESC LIMIT $limit;";

        // Bind parameters using HashMap creation
        let bindings: HashMap<String, surrealdb::sql::Value> = [
            ("agent_id".to_string(), surrealdb::sql::Value::from(agent_id)),
            ("limit".to_string(), surrealdb::sql::Value::from(limit)), // usize should convert
        ]
        .iter().cloned().collect();


        // Execute the query
        let mut response = self.db.query(query)
            .bind(bindings)
            .await
            .map_err(|e| {
                let domain_error = DomainPrismaError::DatabaseError(format!("Failed to query conversation history: {}", e));
                GenericError::from(domain_error)
            })?;

        // Deserialize the resultset
        let history: Vec<T> = response.take(0)
            .map_err(|e| {
                 let domain_error = DomainPrismaError::DatabaseError(format!("Failed to deserialize conversation history: {}", e));
                 GenericError::from(domain_error)
            })?;

        Ok(history)
    }
}

// --- Core data structures (remain the same) ---
#[derive(Debug, Serialize, Deserialize)]
pub struct Embedding {
    pub id: String,
    pub embedding: Vec<f32>,
    pub text: String,
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Document {
    pub id: String,
    pub content: String,
    pub metadata: Option<serde_json::Value>,
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ChatMessage {
    pub id: String,
    // TODO: Add agent_id field if it doesn't exist
    // pub agent_id: String,
    pub role: String,
    pub content: String,
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ShortTermMemory {
    pub id: String,
    pub content: String,
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "datetime_format")]
    pub expires_at: DateTime<Utc>,
}

// --- Database client wrapper (updated for HTTP) ---
#[derive(Debug)]
pub struct Database {
    client: Surreal<Client>,
}

impl Database {
    pub async fn new() -> PrismaResult<Self> {
        let client = Surreal::new::<Http>("127.0.0.1:8000").await
            .map_err(|e| GenericError::from(
                DomainPrismaError::ConnectionError(format!("Failed to connect: {}", e))
            ))?;

        client.signin(Root {
            username: "root",
            password: "root",
        }).await
            .map_err(|e| GenericError::from(
                DomainPrismaError::AuthError(format!("Failed to authenticate: {}", e))
            ))?;

        client.use_ns("test").use_db("test").await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to select namespace/database: {}", e))
            ))?;

        Ok(Self { client })
    }

    pub fn client(&self) -> &Surreal<Client> {
        &self.client
    }

    // Embedding operations
    pub async fn create_embedding(
        &self,
        id: Option<String>,
        embedding: Vec<f32>,
        text: String,
    ) -> PrismaResult<String> {
        let id = id.unwrap_or_else(|| Uuid::new_v4().to_string());
        let embedding_data = Embedding { // Renamed variable to avoid conflict
            id: id.clone(),
            embedding,
            text,
            created_at: Utc::now(),
        };

        // Convert to Value to avoid lifetime issues
        let data_value = serde_json::to_value(&embedding_data)
            .map_err(|e| GenericError::from(
                DomainPrismaError::SerializationError(format!("Failed to serialize embedding data: {}", e))
            ))?;

        let created: Option<Embedding> = self.client
            .create(("embedding", &id))
            .content(data_value)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to create embedding: {}", e))
            ))?;

        created
            .map(|e| e.id)
            .ok_or_else(|| GenericError::from(
                DomainPrismaError::DatabaseError("Failed to create embedding: No record returned".to_string())
            ))
    }

    pub async fn get_embedding(&self, id: &str) -> PrismaResult<Option<Embedding>> {
        let embedding: Option<Embedding> = self.client
            .select(("embedding", id))
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to get embedding: {}", e))
            ))?;

        Ok(embedding)
    }

    // Document operations
    pub async fn create_document(
        &self,
        id: Option<String>,
        content: String,
        metadata: Option<serde_json::Value>,
    ) -> PrismaResult<String> {
        let id = id.unwrap_or_else(|| Uuid::new_v4().to_string());
        let document = Document {
            id: id.clone(),
            content,
            metadata,
            created_at: Utc::now(),
        };

        // Convert to Value to avoid lifetime issues
        let data_value = serde_json::to_value(&document)
            .map_err(|e| GenericError::from(
                DomainPrismaError::SerializationError(format!("Failed to serialize document data: {}", e))
            ))?;

        let created: Option<Document> = self.client
            .create(("document", &id))
            .content(data_value)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to create document: {}", e))
            ))?;

        created
            .map(|d| d.id)
            .ok_or_else(|| GenericError::from(
                DomainPrismaError::DatabaseError("Failed to create document: No record returned".to_string())
            ))
    }

    pub async fn get_document(&self, id: &str) -> PrismaResult<Option<Document>> {
        let document: Option<Document> = self.client
            .select(("document", id))
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to get document: {}", e))
            ))?;

        Ok(document)
    }

    // Chat message operations
    pub async fn create_chat_message(&self, id: Option<String>, role: String, content: String) -> PrismaResult<String> {
        let id = id.unwrap_or_else(|| Uuid::new_v4().to_string());

        let message = ChatMessage {
            id: id.clone(),
            role,
            content,
            created_at: Utc::now(),
        };

        // Convert to Value to avoid lifetime issues
        let data_value = serde_json::to_value(&message)
            .map_err(|e| GenericError::from(
                DomainPrismaError::SerializationError(format!("Failed to serialize chat message: {}", e))
            ))?;

        let created: Option<ChatMessage> = self.client
            .create(("chat_message", &id))
            .content(data_value)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to create chat message: {}", e))
            ))?;

        // Verify we got a record back
        if created.is_none() {
            return Err(GenericError::from(
                DomainPrismaError::DatabaseError("Failed to create chat message: No record returned".to_string())
            ));
        }

        Ok(id)
    }

    pub async fn get_chat_history(&self) -> PrismaResult<Vec<ChatMessage>> {
        let messages: Vec<ChatMessage> = self.client
            .select("chat_message")
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to get chat history: {}", e))
            ))?;
        Ok(messages)
    }

    // Short-term memory operations
    pub async fn create_short_term_memory(&self, id: Option<String>, content: String, expires_at: DateTime<Utc>) -> PrismaResult<String> {
        let id = id.unwrap_or_else(|| Uuid::new_v4().to_string());

        let memory = ShortTermMemory {
            id: id.clone(),
            content,
            created_at: Utc::now(),
            expires_at,
        };

        // Convert to Value to avoid lifetime issues
        let data_value = serde_json::to_value(&memory)
            .map_err(|e| GenericError::from(
                DomainPrismaError::SerializationError(format!("Failed to serialize short-term memory: {}", e))
            ))?;

        let created: Option<ShortTermMemory> = self.client
            .create(("short_term_memory", &id))
            .content(data_value)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to create short-term memory: {}", e))
            ))?;

        // Verify we got a record back
        if created.is_none() {
            return Err(GenericError::from(
                DomainPrismaError::DatabaseError("Failed to create short-term memory: No record returned".to_string())
            ));
        }

        Ok(id)
    }

    pub async fn get_short_term_memory(&self, id: &str) -> PrismaResult<Option<ShortTermMemory>> {
        let memory: Option<ShortTermMemory> = self.client
            .select(("short_term_memory", id))
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to get short-term memory: {}", e))
            ))?;
        Ok(memory)
    }

    pub async fn cleanup_expired_short_term_memories(&self) -> PrismaResult<()> {
        let _deleted = self.client
            .query("DELETE short_term_memory WHERE expires_at <= time::now()")
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to cleanup expired short-term memories: {}", e))
            ))?;
        Ok(())
    }
}

// Helper function to calculate cosine similarity between two vectors
fn cosine_similarity(a: &[f32], b: &[f32]) -> f32 {
    if a.len() != b.len() || a.is_empty() {
        return 0.0;
    }

    let mut dot_product = 0.0;
    let mut a_norm = 0.0;
    let mut b_norm = 0.0;

    for i in 0..a.len() {
        dot_product += a[i] as f64 * b[i] as f64;
        a_norm += a[i] as f64 * a[i] as f64;
        b_norm += b[i] as f64 * b[i] as f64;
    }

    if a_norm == 0.0 || b_norm == 0.0 {
        return 0.0;
    }

    (dot_product / (a_norm.sqrt() * b_norm.sqrt())) as f32
}

// Define a struct for memory data to ensure proper serialization
#[derive(Debug, Serialize, Deserialize)]
struct LongTermMemoryData {
    agent_id: String,
    content: String,
    embedding: Vec<f32>,
    category: String,
    importance: f32,
    #[serde(with = "datetime_format")]
    created_at: DateTime<Utc>,
    access_count: i32,
}

#[async_trait]
impl LongTermMemoryStore for SurrealDbConnection {
    async fn store_memory(
        &self,
        agent_id: &str,
        content: &str,
        category: &str,
        importance: f32,
        embedding: &[f32]
    ) -> PrismaResult<String> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();
        
        // Create the memory data as a JSON object with explicit formatting
        // This avoids the DateTime serialization issue with SurrealDB
        let memory_json = serde_json::json!({
            "agent_id": agent_id,
            "content": content,
            "embedding": embedding,
            "category": category,
            "importance": importance,
            "created_at": now.to_rfc3339(),
            "access_count": 0
        });

        println!("Storing memory with data: {:?}", memory_json);

        // Create the memory record
        let created: Option<Memory> = self.db
            .create(("long_term_memory", &id))
            .content(memory_json)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to store memory: {}", e))
            ))?;

        // Return the full ID format for consistency
        created
            .map(|m| m.id)
            .ok_or_else(|| GenericError::from(
                DomainPrismaError::DatabaseError("Failed to create memory: No record returned".to_string())
            ))
    }

    async fn get_memory(&self, memory_id: &str) -> PrismaResult<Memory> {
        let memory: Option<Memory> = self.db
            .select(("long_term_memory", memory_id))
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to get memory: {}", e))
            ))?;

        // If found, update access stats
        if let Some(mut memory) = memory {
            // Update access stats
            memory.access_count += 1;
            memory.last_accessed = Some(Utc::now());

            // Update in the database
            let _updated: Option<Memory> = self.db
                .update(("long_term_memory", memory_id))
                .merge(serde_json::json!({
                    "access_count": memory.access_count,
                    "last_accessed": memory.last_accessed
                }))
                .await
                .map_err(|e| GenericError::from(
                    DomainPrismaError::DatabaseError(format!("Failed to update memory access stats: {}", e))
                ))?;

            Ok(memory)
        } else {
            Err(GenericError::from(
                DomainPrismaError::NotFoundError(format!("Memory with ID {} not found", memory_id))
            ))
        }
    }

    async fn update_memory(
        &self,
        memory_id: &str,
        content: Option<&str>,
        category: Option<&str>,
        importance: Option<f32>,
        embedding: Option<Vec<f32>>
    ) -> PrismaResult<()> {
        // First check if the memory exists
        let memory: Option<Memory> = self.db
            .select(("long_term_memory", memory_id))
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to get memory for update: {}", e))
            ))?;

        if memory.is_none() {
            return Err(GenericError::from(
                DomainPrismaError::NotFoundError(format!("Memory with ID {} not found", memory_id))
            ));
        }

        // Build update object with only the fields that need to be updated
        let mut update_data = serde_json::Map::new();

        if let Some(content) = content {
            update_data.insert("content".to_string(), serde_json::Value::String(content.to_string()));
        }

        if let Some(category) = category {
            update_data.insert("category".to_string(), serde_json::Value::String(category.to_string()));
        }

        if let Some(importance) = importance {
            update_data.insert("importance".to_string(), serde_json::Value::Number(serde_json::Number::from_f64(importance as f64).unwrap()));
        }

        if let Some(embedding) = embedding {
            let embedding_json: Vec<serde_json::Value> = embedding.iter()
                .map(|&val| serde_json::Value::Number(serde_json::Number::from_f64(val as f64).unwrap()))
                .collect();
            update_data.insert("embedding".to_string(), serde_json::Value::Array(embedding_json));
        }

        // Only update if there's something to update
        if !update_data.is_empty() {
            let _updated: Option<Memory> = self.db
                .update(("long_term_memory", memory_id))
                .merge(serde_json::Value::Object(update_data))
                .await
                .map_err(|e| GenericError::from(
                    DomainPrismaError::DatabaseError(format!("Failed to update memory: {}", e))
                ))?;
        }

        Ok(())
    }

    async fn delete_memory(&self, memory_id: &str) -> PrismaResult<()> {
        let deleted: Option<Memory> = self.db
            .delete(("long_term_memory", memory_id))
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to delete memory: {}", e))
            ))?;

        if deleted.is_some() {
            Ok(())
        } else {
            Err(GenericError::from(
                DomainPrismaError::NotFoundError(format!("Memory with ID {} not found", memory_id))
            ))
        }
    }

    async fn get_all_memories(&self, agent_id: &str) -> PrismaResult<Vec<Memory>> {
        let query = "SELECT * FROM long_term_memory WHERE agent_id = $agent_id ORDER BY created_at DESC";
        let bindings: HashMap<String, surrealdb::sql::Value> = [("agent_id".to_string(), surrealdb::sql::Value::from(agent_id))]
            .iter().cloned().collect();

        let mut response = self.db.query(query)
            .bind(bindings)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to query memories: {}", e))
            ))?;

        let memories: Vec<Memory> = response.take(0)
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to deserialize memories: {}", e))
            ))?;

        Ok(memories)
    }

    async fn get_memories_by_category(&self, agent_id: &str, category: &str) -> PrismaResult<Vec<Memory>> {
        let query = "SELECT * FROM long_term_memory WHERE agent_id = $agent_id AND category = $category ORDER BY created_at DESC";
        let bindings: HashMap<String, surrealdb::sql::Value> = [
            ("agent_id".to_string(), surrealdb::sql::Value::from(agent_id)),
            ("category".to_string(), surrealdb::sql::Value::from(category))
        ].iter().cloned().collect();

        let mut response = self.db.query(query)
            .bind(bindings)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to query memories by category: {}", e))
            ))?;

        let memories: Vec<Memory> = response.take(0)
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to deserialize memories by category: {}", e))
            ))?;

        Ok(memories)
    }

    async fn get_memories_by_importance(&self, agent_id: &str, min_importance: f32) -> PrismaResult<Vec<Memory>> {
        let query = "SELECT * FROM long_term_memory WHERE agent_id = $agent_id AND importance >= $min_importance ORDER BY importance DESC";
        let bindings: HashMap<String, surrealdb::sql::Value> = [
            ("agent_id".to_string(), surrealdb::sql::Value::from(agent_id)),
            ("min_importance".to_string(), surrealdb::sql::Value::from(min_importance))
        ].iter().cloned().collect();

        let mut response = self.db.query(query)
            .bind(bindings)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to query memories by importance: {}", e))
            ))?;

        let memories: Vec<Memory> = response.take(0)
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to deserialize memories by importance: {}", e))
            ))?;

        Ok(memories)
    }

    async fn get_recent_memories(&self, agent_id: &str, limit: usize) -> PrismaResult<Vec<Memory>> {
        let query = "SELECT * FROM long_term_memory WHERE agent_id = $agent_id ORDER BY created_at DESC LIMIT $limit";
        let bindings: HashMap<String, surrealdb::sql::Value> = [
            ("agent_id".to_string(), surrealdb::sql::Value::from(agent_id)),
            ("limit".to_string(), surrealdb::sql::Value::from(limit))
        ].iter().cloned().collect();

        let mut response = self.db.query(query)
            .bind(bindings)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to query recent memories: {}", e))
            ))?;

        let memories: Vec<Memory> = response.take(0)
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to deserialize recent memories: {}", e))
            ))?;

        Ok(memories)
    }

    async fn get_relevant_memories(&self, agent_id: &str, query_embedding: &[f32], limit: usize) -> PrismaResult<Vec<Memory>> {
        // First, get all memories for this agent
        let memories = self.get_all_memories(agent_id).await?;

        // Calculate similarity scores
        let mut scored_memories: Vec<(Memory, f32)> = memories.into_iter()
            .map(|memory| {
                let similarity = cosine_similarity(query_embedding, &memory.embedding);
                (memory, similarity)
            })
            .collect();

        // Sort by similarity score (highest first)
        scored_memories.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        // Take the top 'limit' memories
        let top_memories = scored_memories.into_iter()
            .take(limit)
            .map(|(memory, _)| memory)
            .collect();

        Ok(top_memories)
    }

    fn format_memories_for_prompt(&self, memories: &[Memory]) -> String {
        if memories.is_empty() {
            return String::new();
        }

        let mut formatted = String::from("RELEVANT MEMORIES:\n");

        for (i, memory) in memories.iter().enumerate() {
            formatted.push_str(&format!("{}. [{}] {}\n",
                i + 1,
                memory.category,
                memory.content
            ));
        }

        formatted
    }

    async fn cleanup_old_memories(&self, agent_id: &str, max_age_days: u32, min_importance: f32) -> PrismaResult<usize> {
        let query = "
            DELETE long_term_memory
            WHERE agent_id = $agent_id
            AND created_at < $cutoff_date
            AND importance < $min_importance
        ";

        // Calculate cutoff date
        let now = Utc::now();
        let cutoff_date = now - chrono::Duration::days(max_age_days as i64);

        let bindings: HashMap<String, surrealdb::sql::Value> = [
            ("agent_id".to_string(), surrealdb::sql::Value::from(agent_id)),
            ("cutoff_date".to_string(), surrealdb::sql::Value::from(cutoff_date)),
            ("min_importance".to_string(), surrealdb::sql::Value::from(min_importance))
        ].iter().cloned().collect();

        let response = self.db.query(query)
            .bind(bindings)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to cleanup old memories: {}", e))
            ))?;

        // Try to get the count of deleted records
        // This is a bit of a hack, but SurrealDB doesn't have a direct way to get the count of deleted records
        let count = response.num_statements();

        Ok(count)
    }

    async fn remove_agent_memories(&self, agent_id: &str) -> PrismaResult<usize> {
        let query = "
            DELETE long_term_memory
            WHERE agent_id = $agent_id
        ";

        let bindings: HashMap<String, surrealdb::sql::Value> = [
            ("agent_id".to_string(), surrealdb::sql::Value::from(agent_id))
        ].iter().cloned().collect();

        let response = self.db.query(query)
            .bind(bindings)
            .await
            .map_err(|e| GenericError::from(
                DomainPrismaError::DatabaseError(format!("Failed to remove agent memories: {}", e))
            ))?;

        // Try to get the count of deleted records
        let count = response.num_statements();

        Ok(count)
    }
}
