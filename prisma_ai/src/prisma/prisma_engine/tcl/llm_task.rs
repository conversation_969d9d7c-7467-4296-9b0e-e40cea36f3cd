// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/tcl/llm_task.rs
// =================================================================================================
// Purpose: Implements the LlmTask struct for LLM inference operations. This task type handles
// text generation using large language models.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses LlmInferenceParams for task configuration
// =================================================================================================
// External Dependencies:
// - async_trait: For async trait implementation
// - tracing: For logging
// - crate::err: For error handling
// - crate::prisma::prisma_engine::traits::Task: The main Task trait
// - crate::prisma::prisma_engine::types: For task-related types
// - crate::llm::interface: For LLM service interfaces
// =================================================================================================
// Module Interactions:
// - Implements the Task trait from prisma_engine::traits
// - Uses LLM service interfaces for text generation
// - Created by TaskFactory in tcl.rs
// - Executed by the executor module
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::any::Any;
use tracing::{info, debug, error, warn};
use std::sync::Arc;

use crate::err::{PrismaResult, types::{PrismaError, generics::EngineOperationError}};
use crate::prisma::prisma_engine::traits::Task;
use crate::SeqId; // Removed braces
use crate::prisma::prisma_engine::types::{
    TaskId, TaskPriority, TaskCategory, PrismaScore, ResourceType, ResourceUsage
};
use super::types::LlmInferenceParams;

// Use the interfaces defined in the llm module
use crate::llm::interface::{VocabManager, Inference, BatchOperations, Sampling, StreamingOutput};
use crate::llm::interface::batch::Batch;
use crate::llm::interface::sampling::SamplingParams;
use crate::llm::interface::embedding::EmbeddingGenerator;
use std::fmt; // Moved import here

// Placeholder trait combining necessary LLM operations for this task
// This should eventually be replaced by the actual service handle provided by the engine
#[async_trait]
// Removed duplicate #[async_trait]
pub trait LlmService: VocabManager + Inference + BatchOperations + Sampling + StreamingOutput + EmbeddingGenerator + Send + Sync {}

// Concrete task implementation for LLM Inference
// #[derive(Debug)] // Remove derive Debug
pub struct LlmTask {
    id: TaskId,
    params: LlmInferenceParams,
    priority: TaskPriority,
    seq_id: SeqId, // Add sequence ID field
    // Hold a trait object combining the required interfaces
    llm_service: Arc<dyn LlmService>,
}

// Manual Debug implementation for LlmTask
impl fmt::Debug for LlmTask {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("LlmTask")
         .field("id", &self.id)
         .field("params", &self.params) // Assuming LlmInferenceParams derives Debug
         .field("priority", &self.priority)
         .field("seq_id", &self.seq_id)
         .field("llm_service", &format_args!("Arc<dyn LlmService>")) // Indicate presence
         .finish()
    }
}


impl LlmTask {
    // Updated constructor signature
    pub fn new(
        params: LlmInferenceParams,
        seq_id: SeqId, // Accept sequence ID
        llm_service: Arc<dyn LlmService>,
        priority: Option<TaskPriority>, // Accept optional priority
    ) -> Self {
        LlmTask {
            id: TaskId::new(),
            params,
            priority: priority.unwrap_or(TaskPriority::Normal),
            seq_id, // Store sequence ID
            llm_service,
        }
    }

    /// Execute the LLM task in streaming mode
    async fn execute_streaming(&mut self) -> PrismaResult<Box<dyn Any + Send>> {
        info!("Executing LLM Task {} in streaming mode for model '{}'", self.id, self.params.model_id);

        // Get the buffer size for the token channel
        let buffer_size = self.params.streaming_buffer_size.unwrap_or(100);

        // Clone the Arc<dyn LlmService> to use in the spawned task
        let llm_service_clone = self.llm_service.clone();

        // Create a channel for receiving tokens
        let (sender, mut receiver) = tokio::sync::mpsc::channel::<(i32, String)>(buffer_size);

        // Clone the prompt and prepare parameters for the spawned task
        let prompt = self.params.prompt.clone();
        let max_tokens = self.params.max_tokens.map(|t| t as usize);

        // Start the streaming process in a separate task
        tokio::spawn(async move {
            // Create a mutable reference to the LlmService inside the task
            // We need to use Arc::clone and then get a mutable reference to the service
            // This is safe because we're in a separate task and have exclusive access to the clone
            let service_arc = llm_service_clone;

            // Use a block to limit the scope of the mutable borrow
            let result = {
                // Get a mutable reference to the service
                // This is safe because we have exclusive access to the Arc in this task
                let service_ptr = Arc::as_ptr(&service_arc) as *mut dyn LlmService;
                let llm_service = unsafe { &mut *service_ptr };

                // Get a token channel from the LlmService
                let mut token_receiver = llm_service.get_token_channel(buffer_size).await;

                // Start streaming tokens
                let stream_result = llm_service.stream_tokens(&prompt, max_tokens).await;

                // Forward tokens from the LlmService to our channel
                while let Some((token, text)) = token_receiver.recv().await {
                    if sender.send((token, text)).await.is_err() {
                        // The receiver was dropped, stop forwarding
                        break;
                    }
                }

                stream_result
            };

            // Check the result outside the block where we had the mutable borrow
            if let Err(e) = result {
                error!("Streaming failed: {:?}", e);
                return;
            }
        });

        // Collect the streamed tokens
        let mut generated_text = String::new();
        let mut token_count = 0;

        // Process tokens as they arrive
        while let Some((token, text)) = receiver.recv().await {
            debug!("Received token {} with text '{}'", token, text);
            generated_text.push_str(&text);
            token_count += 1;
        }

        info!("Streaming completed for task {}, received {} tokens", self.id, token_count);

        // Return the complete generated text
        Ok(Box::new(generated_text))
    }
}

#[async_trait]
impl Task for LlmTask {
    fn id(&self) -> TaskId {
        self.id
    }

    fn category(&self) -> TaskCategory {
        TaskCategory::LLMInference
    }

    fn priority(&self) -> TaskPriority {
        self.priority
    }

    /// Estimate resource requirements for an LLM task.
    fn get_prisma_score(&self) -> PrismaScore {
        let mut resources = HashMap::new();
        // Explicitly type as f64
        let mut gpu_usage: f64 = 0.7;
        let mut mem_usage: f64 = 0.5;
        let mut cpu_usage: f64 = 0.2;

        let prompt_len = self.params.prompt.len();
        // Default float literals (0.1, 0.05) are f64, so addition is fine
        if prompt_len > 1024 { mem_usage += 0.1; cpu_usage += 0.1; }
        else if prompt_len > 512 { mem_usage += 0.05; gpu_usage += 0.05; cpu_usage += 0.1; }

        if let Some(max_tokens) = self.params.max_tokens {
            if max_tokens > 512 { gpu_usage += 0.1; mem_usage += 0.1; }
            else if max_tokens > 256 { gpu_usage += 0.05; mem_usage += 0.05; }
        }

        // Use f64 for .min()
        resources.insert(ResourceType::GPU, ResourceUsage(gpu_usage.min(1.0)));
        resources.insert(ResourceType::Memory, ResourceUsage(mem_usage.min(1.0)));
        resources.insert(ResourceType::CPU, ResourceUsage(cpu_usage.min(1.0)));
        resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.1));
        resources.insert(ResourceType::DiskIO, ResourceUsage(0.2));

        debug!("Calculated PrismaScore for LlmTask {}: {:?}", self.id, resources);
        PrismaScore { resources }
    }

    /// Execute the LLM inference logic using the provided LlmService handle.
    fn clone_box(&self) -> Box<dyn Task> {
        // Create a new LlmTask with the same parameters
        Box::new(LlmTask {
            id: self.id,
            params: self.params.clone(),
            priority: self.priority,
            seq_id: self.seq_id,
            llm_service: Arc::clone(&self.llm_service),
        })
    }

    async fn execute(&mut self) -> PrismaResult<Box<dyn Any + Send>> {
        info!("Executing LLM Task {} for model '{}'", self.id, self.params.model_id);
        debug!("LLM Task params: {:?}", self.params);

        // Check if streaming mode is enabled
        if let Some(true) = self.params.streaming {
            return self.execute_streaming().await;
        }

        // 1. Tokenize Prompt
        let prompt_text = &self.params.prompt;
        // Estimate buffer size: prompt length + max generation + buffer
        let max_tokens_estimate = prompt_text.len() + self.params.max_tokens.unwrap_or(128) as usize + 16;
        let mut tokens = vec![0; max_tokens_estimate]; // Use crate::Token type alias
        let n_prompt_tokens = self.llm_service.tokenize(prompt_text, &mut tokens, true, false)
            .map_err(|e| {
                let err_msg = format!("Tokenization failed: {}", e);
                error!("{}", err_msg);
                PrismaError::new(EngineOperationError::new(err_msg))
            })?;
        tokens.truncate(n_prompt_tokens);
        debug!("Tokenized prompt into {} tokens for task {}", n_prompt_tokens, self.id);

        // 2. Process Prompt (Simplified: Decode all at once)
        warn!("LLM Task prompt processing uses simplified single batch decode for task {}", self.id);
        let mut prompt_batch = Batch::new();
        prompt_batch.n_tokens = n_prompt_tokens as i32;
        prompt_batch.token = Some(tokens.clone());
        prompt_batch.pos = Some((0..n_prompt_tokens as i32).collect());
        prompt_batch.n_seq_id = Some(vec![1; n_prompt_tokens]); // Each token belongs to one sequence
        prompt_batch.seq_id = Some(vec![vec![self.seq_id]; n_prompt_tokens]); // Use the task's seq_id
        prompt_batch.logits = Some(vec![0; n_prompt_tokens]);
        if let Some(logits_mut) = prompt_batch.logits.as_mut() {
            if !logits_mut.is_empty() {
                logits_mut[n_prompt_tokens - 1] = 1; // Request logits only for the last token
            }
        }

        match self.llm_service.decode(prompt_batch).await {
            Ok(0) => debug!("Prompt decoded successfully for task {}", self.id),
            Ok(1) => {
                let err_msg = "KV cache full during prompt decoding".to_string();
                error!("{}", err_msg);
                return Err(PrismaError::new(EngineOperationError::new(err_msg)));
            }
            Err(e) => {
                 error!("Prompt decoding failed for task {}: {:?}", self.id, e);
                 return Err(e);
            }
            Ok(other_code) => { // Catch-all for other Ok codes
                let err_msg = format!("Unexpected decode result code during prompt processing: {}", other_code);
                error!("{}", err_msg);
                return Err(PrismaError::new(EngineOperationError::new(err_msg)));
            }
        }

        // 3. Generation Loop
        let mut generated_text = String::new();
        let mut current_tokens = tokens; // Start with prompt tokens
        let max_gen_tokens = self.params.max_tokens.unwrap_or(128);
        let eos_token = self.llm_service.eos(); // Get EOS token

        // Prepare sampling parameters
        let sampling_params = SamplingParams {
            temp: self.params.temperature,
            top_k: self.params.top_k,
            top_p: self.params.top_p,
            // Add other params using default values
            ..Default::default()
        };

        for i in 0..max_gen_tokens {
            // Get logits for the last token using spawn_blocking to avoid runtime blocking
            let llm_service_clone = Arc::clone(&self.llm_service);
            let logits = match tokio::task::spawn_blocking(move || {
                llm_service_clone.get_logits_ith(-1)
            }).await {
                Ok(Some(l)) => l,
                Ok(None) => {
                    let err_msg = "Failed to get logits after decode".to_string();
                    error!("{}", err_msg);
                    return Err(PrismaError::new(EngineOperationError::new(err_msg)));
                }
                Err(e) => {
                    let err_msg = format!("Failed to spawn blocking task for logits: {}", e);
                    error!("{}", err_msg);
                    return Err(PrismaError::new(EngineOperationError::new(err_msg)));
                }
            };

            // Sample the next token using the Sampling trait method
            // Call sample_next_token with slice reference and match the result
            let next_token_result = self.llm_service.sample_next_token(&logits, &sampling_params).await;
            let next_token = match next_token_result {
                 Ok(token) => token,
                 Err(e) => {
                     error!("Sampling failed for task {}: {:?}", self.id, e);
                     // Ensure the error type matches the function's return type (PrismaResult<()>)
                     // Assuming PrismaError can be created from GenericError
                     return Err(PrismaError::from(e));
                 }
             };

            debug!("Sampled token {} (iteration {}) for task {}", next_token, i, self.id);

            if next_token == eos_token {
                debug!("EOS token encountered for task {}.", self.id);
                break;
            }

            // Add token and detokenize piece
            current_tokens.push(next_token);
            let mut piece_buf = [0u8; 8]; // Small buffer for single token piece
            let len = self.llm_service.token_to_piece(next_token, &mut piece_buf, 0, false);
            if len > 0 {
                 match String::from_utf8(piece_buf[..len as usize].to_vec()) {
                    Ok(piece) => generated_text.push_str(&piece),
                    Err(_) => warn!("Failed to decode token piece as UTF-8 for token {}", next_token),
                 }
            } else if len < 0 {
                 warn!("Error detokenizing token {} for task {}", next_token, self.id);
            }

            // Prepare batch for the next token decoding
            let mut next_token_batch = Batch::new();
            next_token_batch.n_tokens = 1;
            next_token_batch.token = Some(vec![next_token]);
            next_token_batch.pos = Some(vec![n_prompt_tokens as i32 + i as i32]); // Position continues
            next_token_batch.n_seq_id = Some(vec![1]); // Each token belongs to one sequence
            next_token_batch.seq_id = Some(vec![vec![self.seq_id]]); // Use the task's seq_id
            next_token_batch.logits = Some(vec![1]); // Get logits for this new token

            // Decode the single token to update KV cache and get next logits
             match self.llm_service.decode(next_token_batch).await {
                Ok(0) => (), // Success
                Ok(1) => {
                    let err_msg = "KV cache full during generation".to_string();
                    error!("{}", err_msg);
                    return Err(PrismaError::new(EngineOperationError::new(err_msg)));
                }
                Err(e) => {
                 error!("Generation decoding failed for task {}: {:?}", self.id, e);
                 return Err(e);
                }
                 Ok(other_code) => { // Catch-all for other Ok codes
                    let err_msg = format!("Unexpected decode result code during generation: {}", other_code);
                    error!("{}", err_msg);
                    return Err(PrismaError::new(EngineOperationError::new(err_msg)));
                 }
            }

            if i == max_gen_tokens - 1 {
                warn!("Max generation tokens ({}) reached for task {}", max_gen_tokens, self.id);
            }
        }

        info!("LLM Task {} completed.", self.id);
        // Return the generated text as a String boxed into Any
         Ok(Box::new(generated_text))
    }
}
