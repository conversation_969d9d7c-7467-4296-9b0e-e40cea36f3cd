// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/tcl/embedding_task.rs
// =================================================================================================
// Purpose: Implements the EmbeddingTask struct for generating vector embeddings from text.
// This task type handles text-to-vector conversion for semantic search and similarity operations.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Uses EmbeddingGenerationParams for task configuration
// =================================================================================================
// External Dependencies:
// - async_trait: For async trait implementation
// - tracing: For logging
// - tokio: For async mutex
// - crate::err: For error handling
// - crate::prisma::prisma_engine::traits::Task: The main Task trait
// - crate::prisma::prisma_engine::types: For task-related types
// - crate::llm::interface::EmbeddingGenerator: For embedding generation capability
// =================================================================================================
// Module Interactions:
// - Implements the Task trait from prisma_engine::traits
// - Uses LLM embedding service interfaces for vector generation
// - Created by TaskFactory in tcl.rs
// - Executed by the executor module
// - Results typically used for storage in vector databases
// =================================================================================================

use async_trait::async_trait;
use std::collections::HashMap;
use std::any::Any;
use tracing::{info, debug, error, warn};
use std::sync::Arc;
use tokio::sync::Mutex; // Use tokio's Mutex

use crate::err::PrismaResult;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{
    TaskId, TaskPriority, TaskCategory, PrismaScore, ResourceType, ResourceUsage
};
use super::types::EmbeddingGenerationParams;

// TODO: Define this properly, likely in llm/interface/mod.rs or a dedicated service file
// Need a trait that includes embedding generation capability
use crate::llm::interface::EmbeddingGenerator; // Assuming this trait exists
use crate::err::GenericError;
use crate::err::types::generics::EngineOperationError; // Corrected import path based on compiler hint
use crate::err::types::PrismaError; // Import PrismaError for explicit use
use std::fmt; // Moved import here

// Placeholder trait combining necessary LLM operations for this task
// This should eventually be replaced by the actual service handle provided by the engine
#[async_trait]
// Removed duplicate #[async_trait]
pub trait LlmEmbeddingService: EmbeddingGenerator + Send + Sync {} // Simplified for now

// Concrete task implementation for Embedding Generation
// #[derive(Debug)] // Remove derive Debug
pub struct EmbeddingTask {
    id: TaskId,
    params: EmbeddingGenerationParams,
    priority: TaskPriority,
    // Hold a trait object for the embedding service
    llm_service: Arc<Mutex<dyn LlmEmbeddingService>>, // Wrap in Mutex
}

// Manual Debug implementation for EmbeddingTask
impl fmt::Debug for EmbeddingTask {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("EmbeddingTask")
         .field("id", &self.id)
         .field("params", &self.params) // Assuming EmbeddingGenerationParams derives Debug
         .field("priority", &self.priority)
         .field("llm_service", &format_args!("Arc<Mutex<dyn LlmEmbeddingService>>")) // Update Debug format
         .finish()
    }
}


impl EmbeddingTask {
    // Updated constructor to accept the embedding service handle
    pub fn new(
        params: EmbeddingGenerationParams,
        priority: Option<TaskPriority>,
        llm_service: Arc<Mutex<dyn LlmEmbeddingService>>, // Update constructor type
    ) -> Self {
        EmbeddingTask {
            id: TaskId::new(),
            params,
            priority: priority.unwrap_or(TaskPriority::Normal),
            llm_service,
        }
    }
}

#[async_trait]
impl Task for EmbeddingTask {
    fn id(&self) -> TaskId {
        self.id
    }

    fn category(&self) -> TaskCategory {
        TaskCategory::EmbeddingGeneration
    }

    fn priority(&self) -> TaskPriority {
        self.priority
    }

    /// Estimate resource requirements for an Embedding task.
    fn get_prisma_score(&self) -> PrismaScore {
        let mut resources = HashMap::new();

        // Base estimates - often less intensive than full inference but still needs GPU/CPU
        // Use f64 as expected by ResourceUsage
        let gpu_usage: f64 = 0.4;
        let mut mem_usage: f64 = 0.3; // Make mutable
        let mut cpu_usage: f64 = 0.3; // Make mutable

        // Adjust based on number of texts (simple heuristic)
        let num_texts = self.params.input_texts.len();
        // Use f64 literals for addition
        if num_texts > 100 { mem_usage += 0.1; cpu_usage += 0.1; } // Default literals are f64
        else if num_texts > 10 { mem_usage += 0.05; cpu_usage += 0.05; }

        // TODO: Incorporate model_id to estimate based on model size

        // Use f64 for .min()
        resources.insert(ResourceType::GPU, ResourceUsage(gpu_usage.min(1.0)));
        resources.insert(ResourceType::Memory, ResourceUsage(mem_usage.min(1.0)));
        resources.insert(ResourceType::CPU, ResourceUsage(cpu_usage.min(1.0)));
        resources.insert(ResourceType::NetworkBandwidth, ResourceUsage(0.05)); // Low network/disk
        resources.insert(ResourceType::DiskIO, ResourceUsage(0.1));

        debug!("Calculated PrismaScore for EmbeddingTask {}: {:?}", self.id, resources);
        PrismaScore { resources }
    }

    fn clone_box(&self) -> Box<dyn Task> {
        // Create a new EmbeddingTask with the same parameters
        Box::new(EmbeddingTask {
            id: self.id,
            params: self.params.clone(),
            priority: self.priority,
            llm_service: Arc::clone(&self.llm_service),
        })
    }

    /// Execute the Embedding generation logic using the provided LlmService handle.
    async fn execute(&mut self) -> PrismaResult<Box<dyn Any + Send>> {
        info!("Executing Embedding Task {} for model '{}'", self.id, self.params.model_id);
        debug!("Embedding Task params: {:?}", self.params);

        // --- Actual Embedding Generation ---
        // TODO: Implement pooling strategy selection based on params.pooling_strategy

        // Lock the mutex to get mutable access
        let mut service = self.llm_service.lock().await; // Use tokio's async lock()
        let embeddings = service.generate_embeddings(
                &self.params.input_texts,
                self.params.normalization,
            ).await
            .map_err(|e| {
                let err_msg = format!("Embedding generation failed for task {}: {:?}", self.id, e);
                error!("{}", err_msg);
                // Use the imported EngineOperationError directly
                PrismaError::new(EngineOperationError::new(err_msg))
            })?;

        info!("Embedding Task {} completed.", self.id);
        // Return the generated embeddings (e.g., Vec<Vec<f32>>) boxed
        Ok(Box::new(embeddings))
    }
}
