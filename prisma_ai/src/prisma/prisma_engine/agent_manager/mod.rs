// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/mod.rs
// =================================================================================================
// Purpose: Declares and exports the Agent Manager module components, which provide
// agent management services for the PrismaAI system. This is the main entry point for the
// agent_manager module, defining the AgentManager struct and re-exporting all submodules.
// =================================================================================================
// Internal Dependencies:
// - types.rs: Defines core types like AgentId, AgentState, AgentRole, etc.
// - traits.rs: Defines traits for agent management components
// - generics.rs: Contains generic utilities for the module
// - agents.rs: Defines the Agent struct
// - agent_registry.rs: Implements agent registration and lookup
// - agent_state.rs: Manages agent runtime state
// - agent_capabilities.rs: Manages agent capabilities
// - agent_communication.rs: Handles agent communication
// - config_integration.rs: Integrates with configuration files
// - ui_integration.rs: Integrates with the UI
// - model_metadata.rs: Manages model metadata
// - model_metadata_trait.rs: Defines the model metadata trait
// =================================================================================================
// External Dependencies:
// - std::collections::HashMap: For storing agent data
// - std::sync::Arc and atomic: For shared ownership and atomic operations
// - tokio::sync::RwLock: For thread-safe access to shared data
// - tracing: For logging
// - crate::SeqId: For sequence ID generation
// - crate::err::PrismaResult: For error handling
// - crate::storage::SurrealDbConnection: For database persistence
// - crate::config::YamlConfigManager: For configuration management
// =================================================================================================
// Module Interactions:
// - Used by PrismaEngine to manage agents
// - Integrates with the LLM module for model metadata
// - Integrates with the storage module for persistence
// - Integrates with the config module for configuration
// - Provides agent management services to other modules
// =================================================================================================

use std::collections::HashMap;
use std::sync::{Arc, atomic::{AtomicI32, Ordering}};
use tokio::sync::RwLock;
use tracing::{debug, info, error, warn};

use crate::SeqId;
use crate::err::PrismaResult;
use crate::storage::SurrealDbConnection;
use crate::config::YamlConfigManager;

// Re-export the agent manager components
pub mod types;
pub mod traits;
pub mod generics;
pub mod agents;
pub mod agent_registry;
pub mod agent_state;
pub mod agent_capabilities;
pub mod agent_communication;
pub mod config_integration;
pub mod ui_integration;
pub mod model_metadata;
pub mod model_metadata_trait;

use self::agents::Agent;
use self::types::{AgentId, AgentState, AgentRole, AgentCapability, AgentMessage};
use self::traits::{AgentRegistry, AgentStateManager, CapabilityManager, AgentCommunicator};
use self::agent_registry::EnhancedAgentRegistry;
use self::agent_state::{AgentStateManagerImpl, ConversationTurn};
use self::agent_capabilities::AgentCapabilityManager;
use self::agent_communication::AgentCommunicationManager;
use self::config_integration::{ConfigIntegrationManager, PromptTemplate};
use self::ui_integration::{UiIntegrationManager, UiAgentData};
use self::model_metadata::ModelMetadataManager;
pub use self::model_metadata_trait::ModelMetadataManagerTrait;

#[derive(Debug)]
struct AgentManagerState {
    agents: HashMap<AgentId, SeqId>,
    next_seq_id: AtomicI32, // Use Atomic for thread-safe incrementing
}

/// Comprehensive manager for all agent-related functionality
#[derive(Debug, Clone)]
pub struct AgentManager {
    // Legacy implementation for backward compatibility
    state: Arc<RwLock<AgentManagerState>>,

    // Enhanced implementation with new components
    registry: Option<Arc<EnhancedAgentRegistry>>,
    state_manager: Option<Arc<AgentStateManagerImpl>>,
    capability_manager: Option<Arc<AgentCapabilityManager>>,
    communication_manager: Option<Arc<AgentCommunicationManager>>,
    config_manager: Option<Arc<ConfigIntegrationManager>>,
    ui_manager: Option<Arc<UiIntegrationManager>>,
    metadata_manager: Option<Arc<ModelMetadataManager>>,

    // Flag to indicate if using enhanced mode
    enhanced_mode: bool,
}

impl AgentManager {
    /// Creates a new AgentManager with basic functionality (legacy mode)
    pub fn new() -> Self {
        info!("Initializing basic AgentManager");
        // Start SeqId counter from 1 (SeqId 0 might be reserved or default)
        let initial_state = AgentManagerState {
            agents: HashMap::new(),
            next_seq_id: AtomicI32::new(1),
        };
        AgentManager {
            state: Arc::new(RwLock::new(initial_state)),
            registry: None,
            state_manager: None,
            capability_manager: None,
            communication_manager: None,
            config_manager: None,
            ui_manager: None,
            metadata_manager: None,
            enhanced_mode: false,
        }
    }

    /// Creates a new AgentManager with all enhanced components
    pub fn new_enhanced(
        db_connection: Option<Arc<SurrealDbConnection>>,
        yaml_config_manager: Option<Arc<YamlConfigManager>>,
        pm_path: Option<&str>,
        prompt_template_path: Option<&str>,
    ) -> Self {
        info!("Initializing enhanced AgentManager");

        // Create the component managers
        let registry = Arc::new(EnhancedAgentRegistry::new(db_connection.clone()));
        let state_manager = Arc::new(AgentStateManagerImpl::new(db_connection.clone()));
        let capability_manager = Arc::new(AgentCapabilityManager::new(db_connection.clone()));
        let communication_manager = Arc::new(AgentCommunicationManager::new(
            db_connection.clone(),
            yaml_config_manager.clone(),
        ));

        // Create the configuration manager
        let config_manager = if let (Some(pm), Some(template)) = (pm_path, prompt_template_path) {
            Some(Arc::new(ConfigIntegrationManager::new(pm, template)))
        } else {
            // Default paths if not provided
            let pm = "configs/pm.toml";
            let template = "configs/prompt_template.toml";
            warn!("Using default paths for configuration files: {} and {}", pm, template);
            Some(Arc::new(ConfigIntegrationManager::new(pm, template)))
        };

        // Also initialize the legacy state for backward compatibility
        let initial_state = AgentManagerState {
            agents: HashMap::new(),
            next_seq_id: AtomicI32::new(1),
        };

        // Create the UI integration manager
        let ui_manager = if let Some(config_manager_ref) = &config_manager {
            Some(Arc::new(UiIntegrationManager::new(Some(config_manager_ref.clone()))))
        } else {
            Some(Arc::new(UiIntegrationManager::new(None)))
        };

        // Create the model metadata manager
        let metadata_manager = Some(Arc::new(ModelMetadataManager::new()));

        Self {
            state: Arc::new(RwLock::new(initial_state)),
            registry: Some(registry),
            state_manager: Some(state_manager),
            capability_manager: Some(capability_manager),
            communication_manager: Some(communication_manager),
            config_manager,
            ui_manager,
            metadata_manager,
            enhanced_mode: true,
        }
    }

    /// Checks if the AgentManager is in enhanced mode
    pub fn is_enhanced_mode(&self) -> bool {
        self.enhanced_mode
    }

    /// Checks if an agent is already registered.
    pub async fn is_agent_registered(&self, agent_id: &AgentId) -> bool {
        if self.enhanced_mode {
            if let Some(registry) = &self.registry {
                match registry.is_agent_registered(agent_id).await {
                    Ok(result) => return result,
                    Err(e) => {
                        error!("Error checking if agent is registered: {}", e);
                        return false;
                    }
                }
            }
        }

        // Fall back to legacy implementation
        let state_read = self.state.read().await;
        state_read.agents.contains_key(agent_id)
    }

    /// Registers a new agent if it doesn't exist and assigns a unique SeqId.
    /// Returns the assigned SeqId (either new or existing).
    pub async fn register_or_get_agent(&self, agent_id: AgentId) -> PrismaResult<SeqId> {
        if self.enhanced_mode {
            if let Some(registry) = &self.registry {
                return registry.register_or_get_agent(agent_id).await;
            }
        }

        // Fall back to legacy implementation
        // Check if agent already exists with a read lock first
        {
            let state_read = self.state.read().await;
            if let Some(seq_id) = state_read.agents.get(&agent_id) {
                debug!("Agent '{}' already registered with SeqId: {}", agent_id, seq_id);
                return Ok(*seq_id);
            }
        } // Read lock is released here

        // Agent doesn't exist, acquire write lock to register
        let mut state_write = self.state.write().await;

        // Double-check in case another thread registered it between read unlock and write lock
        if let Some(seq_id) = state_write.agents.get(&agent_id) {
             debug!("Agent '{}' registered concurrently with SeqId: {}", agent_id, seq_id);
            return Ok(*seq_id);
        }

        // Assign the next available SeqId with bounds checking
        let new_seq_id = loop {
            let current = state_write.next_seq_id.load(Ordering::SeqCst);
            let next = if current >= 63 { 1 } else { current + 1 }; // Wrap around at LLAMA_MAX_SEQ

            // Use compare_exchange to handle concurrent access
            match state_write.next_seq_id.compare_exchange(current, next, Ordering::SeqCst, Ordering::SeqCst) {
                Ok(_) => break current, // Successfully updated, use current value
                Err(_) => continue, // Another thread updated it, try again
            }
        };

        state_write.agents.insert(agent_id.clone(), new_seq_id);
        info!("Registered new agent '{}' with SeqId: {}", agent_id, new_seq_id);
        Ok(new_seq_id)
    }

    /// Retrieves the SeqId for a given agent ID.
    pub async fn get_seq_id(&self, agent_id: &AgentId) -> PrismaResult<Option<SeqId>> {
        if self.enhanced_mode {
            if let Some(registry) = &self.registry {
                return registry.get_seq_id(agent_id).await;
            }
        }

        // Fall back to legacy implementation
        let state_read = self.state.read().await;
        let seq_id = state_read.agents.get(agent_id).copied();
        if seq_id.is_none() {
            debug!("Attempted to get SeqId for unregistered agent '{}'", agent_id);
        }
        Ok(seq_id)
    }

    /// Creates a new agent with the given ID, name, and role (enhanced mode only)
    pub async fn create_agent(&self, agent_id: String, name: String, role: AgentRole) -> PrismaResult<Agent> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        let state_manager = self.state_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("State manager not initialized")
        })?;

        let agent = registry.create_agent(agent_id.clone(), name, role).await?;

        // Initialize the agent's state
        state_manager.set_state(&agent_id, AgentState::Initializing).await?;

        Ok(agent)
    }

    /// Creates a new agent with the given ID, name, and multiple roles (enhanced mode only)
    pub async fn create_agent_with_roles(&self, agent_id: String, name: String, roles: Vec<AgentRole>) -> PrismaResult<Agent> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        let state_manager = self.state_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("State manager not initialized")
        })?;

        let agent = registry.create_agent_with_roles(agent_id.clone(), name, roles).await?;

        // Initialize the agent's state
        state_manager.set_state(&agent_id, AgentState::Initializing).await?;

        Ok(agent)
    }

    /// Creates a role from a UI role string (enhanced mode only)
    pub fn create_role_from_ui(&self, role_name: String, capabilities: Option<Vec<AgentCapability>>) -> AgentRole {
        // Create a new role with the given name
        // If capabilities are provided, use them; otherwise, create a role with no capabilities
        match capabilities {
            Some(caps) => AgentRole::with_capabilities(role_name, caps),
            None => AgentRole::new(role_name),
        }
    }

    /// Adds a role to an agent (enhanced mode only)
    pub async fn add_role_to_agent(&self, agent_id: &str, role: AgentRole) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        registry.add_role_to_agent(&agent_id.to_string(), role).await
    }

    /// Removes a role from an agent (enhanced mode only)
    pub async fn remove_role_from_agent(&self, agent_id: &str, role_name: &str) -> PrismaResult<bool> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        registry.remove_role_from_agent(&agent_id.to_string(), role_name).await
    }

    /// Gets all roles for an agent (enhanced mode only)
    pub async fn get_agent_roles(&self, agent_id: &str) -> PrismaResult<Vec<AgentRole>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        let agent = registry.get_agent(&agent_id.to_string()).await?;

        match agent {
            Some(agent) => Ok(agent.roles),
            None => Err(crate::err::GenericError::from(format!("Agent {} not found", agent_id))),
        }
    }

    /// Gets all agents with a specific role (enhanced mode only)
    pub async fn get_agents_by_role_name(&self, role_name: &str) -> PrismaResult<Vec<Agent>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        registry.get_agents_by_role_name(role_name).await
    }

    /// Unregisters an agent and cleans up all related resources (enhanced mode only)
    pub async fn unregister_agent(&self, agent_id: &str) -> PrismaResult<bool> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        let state_manager = self.state_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("State manager not initialized")
        })?;

        // Set the agent state to Terminated
        if let Ok(Some(_)) = state_manager.get_state(&agent_id.to_string()).await {
            state_manager.set_state(&agent_id.to_string(), AgentState::Terminated).await?;
        }

        // Unregister from the registry
        registry.unregister_agent(&agent_id.to_string()).await
    }

    /// Gets a complete agent by ID (enhanced mode only)
    pub async fn get_agent(&self, agent_id: &str) -> PrismaResult<Option<Agent>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        registry.get_agent(&agent_id.to_string()).await
    }

    /// Gets all registered agent IDs (enhanced mode only)
    pub async fn get_all_agent_ids(&self) -> PrismaResult<Vec<String>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        registry.get_all_agent_ids().await
    }

    /// Gets all agents (enhanced mode only)
    pub async fn get_all_agents(&self) -> PrismaResult<Vec<Agent>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        registry.get_all_agents().await
    }

    /// Gets the current state of an agent (enhanced mode only)
    pub async fn get_agent_state(&self, agent_id: &str) -> PrismaResult<Option<AgentState>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let state_manager = self.state_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("State manager not initialized")
        })?;

        state_manager.get_state(&agent_id.to_string()).await
    }

    /// Sets the state of an agent (enhanced mode only)
    pub async fn set_agent_state(&self, agent_id: &str, state: AgentState) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let state_manager = self.state_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("State manager not initialized")
        })?;

        state_manager.set_state(&agent_id.to_string(), state).await
    }

    /// Adds a capability to an agent (enhanced mode only)
    pub async fn add_agent_capability(&self, agent_id: &str, capability: AgentCapability) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let capability_manager = self.capability_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Capability manager not initialized")
        })?;

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        // First, add the capability to the capability cache
        capability_manager.add_capability(&agent_id.to_string(), capability.clone()).await?;

        // Then, retrieve the agent from the registry
        if let Some(mut agent) = registry.get_agent(&agent_id.to_string()).await? {
            // Add the capability to the agent's in-memory capabilities
            agent.add_capability(capability);

            // Update the agent in the registry
            registry.update_agent(agent).await?;
        }

        Ok(())
    }

    /// Checks if an agent has a specific capability (enhanced mode only)
    pub async fn agent_has_capability(&self, agent_id: &str, capability: &AgentCapability) -> PrismaResult<bool> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let capability_manager = self.capability_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Capability manager not initialized")
        })?;

        capability_manager.has_capability(&agent_id.to_string(), capability).await
    }

    /// Sends a message from one agent to another (enhanced mode only)
    pub async fn send_agent_message(
        &self,
        from: &str,
        to: &str,
        content: String,
        priority: Option<types::MessagePriority>,
    ) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        // Use the provided priority or default to Normal
        let priority = priority.unwrap_or(types::MessagePriority::Normal);

        communication_manager.send_agent_to_agent(
            &from.to_string(),
            &to.to_string(),
            content,
            priority
        ).await
    }

    /// Sends a message from an agent to a human (enhanced mode only)
    pub async fn send_agent_to_human(
        &self,
        from: &str,
        to: &str,
        content: String,
        priority: Option<types::MessagePriority>,
    ) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        // Use the provided priority or default to Normal
        let priority = priority.unwrap_or(types::MessagePriority::Normal);

        communication_manager.send_agent_to_human(
            &from.to_string(),
            &to.to_string(),
            content,
            priority
        ).await
    }

    /// Broadcasts a message to multiple agents (enhanced mode only)
    pub async fn broadcast_to_agents(
        &self,
        from: &str,
        to: &[String],
        content: String,
        priority: Option<types::MessagePriority>,
    ) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        // Use the provided priority or default to Normal
        let priority = priority.unwrap_or(types::MessagePriority::Normal);

        communication_manager.broadcast_to_agents(
            &from.to_string(),
            to,
            content,
            priority
        ).await
    }

    /// Broadcasts a message to multiple humans (enhanced mode only)
    pub async fn broadcast_to_humans(
        &self,
        from: &str,
        to: &[String],
        content: String,
        priority: Option<types::MessagePriority>,
    ) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        // Use the provided priority or default to Normal
        let priority = priority.unwrap_or(types::MessagePriority::Normal);

        communication_manager.broadcast_to_humans(
            &from.to_string(),
            to,
            content,
            priority
        ).await
    }

    /// Gets all messages sent to an agent (enhanced mode only)
    pub async fn get_agent_messages(&self, agent_id: &str) -> PrismaResult<Vec<AgentMessage>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        communication_manager.get_messages(&agent_id.to_string()).await
    }

    /// Gets the conversation between two agents (enhanced mode only)
    pub async fn get_agent_conversation(&self, agent1: &str, agent2: &str) -> PrismaResult<Vec<AgentMessage>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        communication_manager.get_agent_conversation(&agent1.to_string(), &agent2.to_string()).await
    }

    /// Adds a conversation turn for an agent (enhanced mode only)
    pub async fn add_conversation_turn(&self, agent_id: &str, user_message: String, agent_response: String) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let state_manager = self.state_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("State manager not initialized")
        })?;

        state_manager.add_conversation_turn(&agent_id.to_string(), user_message, agent_response).await
    }

    /// Gets the conversation history for an agent (enhanced mode only)
    pub async fn get_conversation_history(&self, agent_id: &str) -> PrismaResult<Vec<ConversationTurn>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let state_manager = self.state_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("State manager not initialized")
        })?;

        state_manager.get_conversation_history(&agent_id.to_string()).await
    }

    /// Sets an agent as busy with a specific task (enhanced mode only)
    pub async fn set_agent_busy(&self, agent_id: &str, task_id: String) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let state_manager = self.state_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("State manager not initialized")
        })?;

        state_manager.set_agent_busy(&agent_id.to_string(), task_id).await
    }

    /// Sets an agent as idle (enhanced mode only)
    pub async fn set_agent_idle(&self, agent_id: &str) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let state_manager = self.state_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("State manager not initialized")
        })?;

        state_manager.set_agent_idle(&agent_id.to_string()).await
    }

    /// Checks if an agent is busy (enhanced mode only)
    pub async fn is_agent_busy(&self, agent_id: &str) -> PrismaResult<bool> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let state_manager = self.state_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("State manager not initialized")
        })?;

        state_manager.is_agent_busy(&agent_id.to_string()).await
    }

    /// Gets all messages sent to a human (enhanced mode only)
    pub async fn get_human_messages(&self, human_id: &str) -> PrismaResult<Vec<AgentMessage>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        communication_manager.get_human_messages(&human_id.to_string()).await
    }

    /// Gets the conversation between an agent and a human (enhanced mode only)
    pub async fn get_human_conversation(&self, agent_id: &str, human_id: &str) -> PrismaResult<Vec<AgentMessage>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        communication_manager.get_human_conversation(&agent_id.to_string(), &human_id.to_string()).await
    }

    /// Sets whether an agent can communicate with humans (enhanced mode only)
    pub async fn set_human_communication_enabled(&self, agent_id: &str, enabled: bool) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        communication_manager.set_human_communication_enabled(&agent_id.to_string(), enabled).await
    }

    /// Checks if an agent can communicate with humans (enhanced mode only)
    pub async fn can_communicate_with_humans(&self, agent_id: &str) -> PrismaResult<bool> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        communication_manager.can_communicate_with_humans(&agent_id.to_string()).await
    }

    /// Initializes the RabbitMQ publisher (enhanced mode only)
    pub async fn init_rabbitmq(&self) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let communication_manager = self.communication_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Communication manager not initialized")
        })?;

        // Since we can't modify the AgentCommunicationManager directly, we need to call
        // a method that initializes RabbitMQ internally
        communication_manager.init_rabbitmq().await
    }

    /// Loads configuration from pm.toml and prompt_template.toml (enhanced mode only)
    pub async fn load_configuration(&self) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let config_manager = self.config_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Configuration manager not initialized")
        })?;

        // Load both configuration files
        config_manager.load_pm_config().await?;
        config_manager.load_prompt_template_config().await?;

        info!("Configuration loaded successfully");
        Ok(())
    }

    /// Loads agent configurations from pm.toml and creates agents (enhanced mode only)
    pub async fn load_agents_from_config(&self) -> PrismaResult<Vec<Agent>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let config_manager = self.config_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Configuration manager not initialized")
        })?;

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        // Load agent configurations from pm.toml
        let agent_configs = config_manager.load_agents().await?;

        let mut agents = Vec::new();

        // Create agents from configurations
        for (agent_id, config) in agent_configs {
            // Check if the agent already exists
            if let Ok(Some(existing_agent)) = registry.get_agent(&agent_id).await {
                // Update the existing agent's configuration
                let mut agent = existing_agent.clone();
                agent.config = config;
                registry.update_agent(agent.clone()).await?;
                agents.push(agent);
            } else {
                // Create a new agent
                let name = agent_id.clone(); // Use agent_id as name if not specified
                let role = AgentRole::new("Default"); // Use a default role
                let mut agent = registry.create_agent(agent_id.clone(), name, role).await?;
                agent.config = config;
                registry.update_agent(agent.clone()).await?;
                agents.push(agent);
            }
        }

        info!("Loaded {} agents from configuration", agents.len());
        Ok(agents)
    }

    /// Saves an agent's configuration to pm.toml (enhanced mode only)
    pub async fn save_agent_to_config(&self, agent_id: &str) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let config_manager = self.config_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Configuration manager not initialized")
        })?;

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        // Get the agent
        let agent = registry.get_agent(&agent_id.to_string()).await?.ok_or_else(|| {
            crate::err::GenericError::from(format!("Agent {} not found", agent_id))
        })?;

        // Update the agent's configuration in pm.toml
        config_manager.update_agent_from_struct(&agent).await?;

        info!("Saved agent {} to configuration", agent_id);
        Ok(())
    }

    /// Removes an agent's configuration from pm.toml (enhanced mode only)
    pub async fn remove_agent_from_config(&self, agent_id: &str) -> PrismaResult<bool> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let config_manager = self.config_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Configuration manager not initialized")
        })?;

        // Remove the agent's configuration from pm.toml
        let removed = config_manager.remove_agent_config(&agent_id.to_string()).await?;

        if removed {
            info!("Removed agent {} from configuration", agent_id);
        } else {
            warn!("Agent {} not found in configuration", agent_id);
        }

        Ok(removed)
    }

    /// Gets a prompt template for an agent (enhanced mode only)
    pub async fn get_prompt_template(&self, template_key: &str) -> PrismaResult<Option<PromptTemplate>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let config_manager = self.config_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Configuration manager not initialized")
        })?;

        config_manager.get_prompt_template(template_key).await
    }

    /// Updates a prompt template for an agent (enhanced mode only)
    pub async fn update_prompt_template(&self, template_key: &str, template: PromptTemplate) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let config_manager = self.config_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Configuration manager not initialized")
        })?;

        config_manager.update_prompt_template(template_key, template).await
    }

    /// Sets an agent's template key (enhanced mode only)
    pub async fn set_agent_template_key(&self, agent_id: &str, template_key: &str) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        // Get the agent
        let mut agent = registry.get_agent(&agent_id.to_string()).await?.ok_or_else(|| {
            crate::err::GenericError::from(format!("Agent {} not found", agent_id))
        })?;

        // Set the template key
        agent.set_template_key(template_key.to_string());

        // Update the agent
        registry.update_agent(agent).await?;

        // Save the agent's configuration to pm.toml
        self.save_agent_to_config(agent_id).await?;

        info!("Set template key for agent {} to {}", agent_id, template_key);
        Ok(())
    }

    /// Extracts metadata from a model and associates it with an agent (enhanced mode only)
    pub async fn extract_model_metadata<T: crate::llm::interface::ModelMetadata + Send + Sync + 'static>(
        &self,
        agent_id: &str,
        model: &T,
        model_id: &str
    ) -> PrismaResult<serde_json::Value> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let _metadata_manager = self.metadata_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Metadata manager not initialized")
        })?;

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        // Extract metadata from the model
        // Create a new instance to avoid mutating the shared one
        let metadata_manager_instance = model_metadata::ModelMetadataManager::new();
        let metadata = metadata_manager_instance.extract_metadata(model, model_id).await?;

        // Get the agent
        let mut agent = registry.get_agent(&agent_id.to_string()).await?.ok_or_else(|| {
            crate::err::GenericError::from(format!("Agent {} not found", agent_id))
        })?;

        // Set the model metadata
        agent.set_model_metadata(metadata.clone());

        // Update the agent
        registry.update_agent(agent).await?;

        // Save the agent's configuration to pm.toml
        self.save_agent_to_config(agent_id).await?;

        info!("Set model metadata for agent {} using model {}", agent_id, model_id);
        Ok(metadata)
    }

    /// Gets model metadata for an agent (enhanced mode only)
    pub async fn get_agent_model_metadata(&self, agent_id: &str) -> PrismaResult<Option<serde_json::Value>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        // Get the agent
        let agent = registry.get_agent(&agent_id.to_string()).await?.ok_or_else(|| {
            crate::err::GenericError::from(format!("Agent {} not found", agent_id))
        })?;

        // Get the model metadata
        Ok(agent.get_model_metadata().cloned())
    }

    /// Gets model metadata for a specific model (enhanced mode only)
    pub async fn get_model_metadata(&self, model_id: &str) -> PrismaResult<Option<serde_json::Value>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let metadata_manager = self.metadata_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Metadata manager not initialized")
        })?;

        // Get the model metadata
        Ok(metadata_manager.get_metadata(model_id))
    }

    /// Gets a specific metadata field for a model (enhanced mode only)
    pub async fn get_model_metadata_field(&self, model_id: &str, field: &str) -> PrismaResult<Option<serde_json::Value>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let metadata_manager = self.metadata_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Metadata manager not initialized")
        })?;

        // Get the model metadata field
        Ok(metadata_manager.get_metadata_field(model_id, field))
    }

    /// Extracts model capabilities from metadata (enhanced mode only)
    pub async fn extract_model_capabilities(&self, model_id: &str) -> PrismaResult<Vec<String>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let metadata_manager = self.metadata_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Metadata manager not initialized")
        })?;

        // Get the model metadata
        let metadata = metadata_manager.get_metadata(model_id).ok_or_else(|| {
            crate::err::GenericError::from(format!("Metadata for model {} not found", model_id))
        })?;

        // Extract capabilities
        Ok(model_metadata::extract_model_capabilities(&metadata))
    }

    /// Extracts recommended roles from metadata (enhanced mode only)
    pub async fn extract_recommended_roles(&self, model_id: &str) -> PrismaResult<Vec<String>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let metadata_manager = self.metadata_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Metadata manager not initialized")
        })?;

        // Get the model metadata
        let metadata = metadata_manager.get_metadata(model_id).ok_or_else(|| {
            crate::err::GenericError::from(format!("Metadata for model {} not found", model_id))
        })?;

        // Extract recommended roles
        Ok(model_metadata::extract_recommended_roles(&metadata))
    }

    /// Creates an agent from UI data (enhanced mode only)
    pub async fn create_agent_from_ui(&self, ui_data: UiAgentData) -> PrismaResult<Agent> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let ui_manager = self.ui_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("UI manager not initialized")
        })?;

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        // Convert UI data to Agent
        let agent = ui_manager.ui_data_to_agent(ui_data)?;

        // Add the agent to the registry
        registry.add_agent(agent.clone()).await?;

        // Save the agent's configuration to pm.toml
        self.save_agent_to_config(&agent.id).await?;

        info!("Created agent {} from UI data", agent.id);
        Ok(agent)
    }

    /// Updates an agent from UI data (enhanced mode only)
    pub async fn update_agent_from_ui(&self, agent_id: &str, ui_data: UiAgentData) -> PrismaResult<Agent> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let ui_manager = self.ui_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("UI manager not initialized")
        })?;

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        // Get the agent
        let mut agent = registry.get_agent(&agent_id.to_string()).await?.ok_or_else(|| {
            crate::err::GenericError::from(format!("Agent {} not found", agent_id))
        })?;

        // Update the agent from UI data
        ui_manager.update_agent_from_ui(&mut agent, ui_data).await?;

        // Update the agent in the registry
        registry.update_agent(agent.clone()).await?;

        // Save the agent's configuration to pm.toml
        self.save_agent_to_config(agent_id).await?;

        info!("Updated agent {} from UI data", agent_id);
        Ok(agent)
    }

    /// Converts an agent to UI data (enhanced mode only)
    pub async fn agent_to_ui_data(&self, agent_id: &str) -> PrismaResult<UiAgentData> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let ui_manager = self.ui_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("UI manager not initialized")
        })?;

        let registry = self.registry.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("Registry not initialized")
        })?;

        // Get the agent
        let agent = registry.get_agent(&agent_id.to_string()).await?.ok_or_else(|| {
            crate::err::GenericError::from(format!("Agent {} not found", agent_id))
        })?;

        // Convert the agent to UI data
        let ui_data = ui_manager.agent_to_ui_data(&agent);

        Ok(ui_data)
    }

    /// Registers a UI-defined role (enhanced mode only)
    pub async fn register_ui_role(&self, name: &str, description: Option<String>, capabilities: Option<Vec<String>>) -> PrismaResult<AgentRole> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let ui_manager = self.ui_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("UI manager not initialized")
        })?;

        ui_manager.register_ui_role(name, description, capabilities).await
    }

    /// Gets a UI-defined role (enhanced mode only)
    pub async fn get_ui_role(&self, name: &str) -> PrismaResult<Option<AgentRole>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let ui_manager = self.ui_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("UI manager not initialized")
        })?;

        Ok(ui_manager.get_ui_role(name).await)
    }

    /// Gets all UI-defined roles (enhanced mode only)
    pub async fn get_all_ui_roles(&self) -> PrismaResult<HashMap<String, AgentRole>> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let ui_manager = self.ui_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("UI manager not initialized")
        })?;

        Ok(ui_manager.get_all_ui_roles().await)
    }

    /// Synchronizes UI-defined roles with the agent manager (enhanced mode only)
    pub async fn sync_ui_roles(&self, roles: Vec<(String, Option<String>, Option<Vec<String>>)>) -> PrismaResult<()> {
        if !self.enhanced_mode {
            return Err(crate::err::GenericError::from("AgentManager not in enhanced mode"));
        }

        let ui_manager = self.ui_manager.as_ref().ok_or_else(|| {
            crate::err::GenericError::from("UI manager not initialized")
        })?;

        ui_manager.sync_ui_roles(roles).await
    }
}

// Default implementation
impl Default for AgentManager {
    fn default() -> Self {
        Self::new()
    }
}
