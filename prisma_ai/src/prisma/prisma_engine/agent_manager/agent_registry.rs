// =================================================================================================
// File: /prisma_ai/src/prisma/prisma_engine/agent_manager/agent_registry.rs
// =================================================================================================
// Purpose: Implements the AgentRegistry trait for managing agent registration and lifecycle.
// This file provides a sophisticated registry for tracking agents, their roles, and their
// sequence IDs. It supports persistence to a database and maintains an in-memory cache for
// quick access to agent information.
// =================================================================================================
// Internal Dependencies:
// - agents.rs: Uses the Agent struct for agent representation
// - types.rs: Uses AgentId and AgentRole types
// - traits.rs: Implements the AgentRegistry trait
// - generics.rs: Uses the Cache generic for storing agents
// =================================================================================================
// External Dependencies:
// - std::collections::HashMap: For mapping agent IDs to sequence IDs
// - tokio::sync::RwLock: For thread-safe access to shared data
// - tracing: For logging
// - crate::SeqId: For sequence ID generation
// - crate::err::PrismaResult: For error handling
// - crate::storage::SurrealDbConnection: For database persistence
// =================================================================================================
// Module Interactions:
// - Used by AgentManager to register and manage agents
// - Interacts with the database for persistent storage of agent information
// - Provides agent lookup by ID, role, and other criteria
// - Maintains a sequence ID mapping for agent identification
// - Supports adding and removing roles from agents
// =================================================================================================

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};
use serde::{Serialize, Deserialize};
use serde_json::json;
use chrono::Utc;

use crate::SeqId;
use crate::err::{PrismaResult, GenericError};
use crate::storage::{SurrealDbConnection, DataStore};

use super::agents::Agent;
use super::types::{AgentId, AgentRole, AgentState};
use super::traits::AgentRegistry;
use super::generics::Cache;

/// Database representation of an agent
#[derive(Debug, Serialize, Deserialize)]
struct AgentRecord {
    /// Unique identifier for the agent
    id: String,

    /// Sequence ID for LLM context management
    seq_id: SeqId,

    /// Human-readable name of the agent
    name: String,

    /// Optional description of the agent's purpose
    description: Option<String>,

    /// Current state of the agent (active, paused, etc.)
    state: String,

    /// The agent's roles in the system
    roles: Vec<AgentRoleRecord>,

    /// When the agent was created
    #[serde(with = "chrono::serde::ts_seconds")]
    created_at: chrono::DateTime<chrono::Utc>,

    /// When the agent was last active
    #[serde(with = "chrono::serde::ts_seconds")]
    last_active: chrono::DateTime<chrono::Utc>,

    /// Preferred LLM model for this agent
    model_preference: Option<String>,

    /// Template key for prompt construction
    template_key: Option<String>,

    /// Whether this agent can initiate communication with humans
    can_communicate_with_humans: bool,

    /// Configuration for this agent (from pm.toml)
    config: serde_json::Value,
}

/// Database representation of an agent role
#[derive(Debug, Serialize, Deserialize)]
struct AgentRoleRecord {
    /// Name of the role
    name: String,

    /// Description of the role
    description: Option<String>,

    /// Capabilities associated with this role
    capabilities: Vec<String>,
}

impl AgentRecord {
    /// Convert an Agent to an AgentRecord for database storage
    fn from_agent(agent: &Agent) -> Self {
        // Convert agent state to string
        let state_str = match agent.state {
            AgentState::Initializing => "initializing",
            AgentState::Active => "active",
            AgentState::Paused => "paused",
            AgentState::Terminated => "terminated",
        };

        // Convert roles to role records
        let roles = agent.roles.iter().map(|role| {
            AgentRoleRecord {
                name: role.name.clone(),
                description: role.description.clone(),
                capabilities: role.capabilities.iter().map(|cap| cap.name()).collect(),
            }
        }).collect();

        // Convert agent config to JSON
        let config = serde_json::to_value(&agent.config).unwrap_or_else(|_| json!({}));

        Self {
            id: agent.id.clone(),
            seq_id: agent.seq_id,
            name: agent.name.clone(),
            description: agent.description.clone(),
            state: state_str.to_string(),
            roles,
            created_at: agent.created_at,
            last_active: agent.last_active,
            model_preference: agent.model_preference.clone(),
            template_key: agent.template_key.clone(),
            can_communicate_with_humans: agent.can_communicate_with_humans,
            config,
        }
    }

    /// Convert an AgentRecord to an Agent
    fn to_agent(&self) -> PrismaResult<Agent> {
        // Convert state string to AgentState
        let state = match self.state.as_str() {
            "initializing" => AgentState::Initializing,
            "active" => AgentState::Active,
            "paused" => AgentState::Paused,
            "terminated" => AgentState::Terminated,
            _ => {
                warn!("Unknown agent state: {}, defaulting to Initializing", self.state);
                AgentState::Initializing
            }
        };

        // Convert role records to roles
        let roles = self.roles.iter().map(|role_record| {
            let capabilities = role_record.capabilities.iter()
                .map(|cap_str| AgentRole::capability_from_string(cap_str))
                .collect();

            AgentRole {
                name: role_record.name.clone(),
                description: role_record.description.clone(),
                capabilities,
            }
        }).collect();

        // Parse agent config from JSON
        let config = serde_json::from_value(self.config.clone())
            .map_err(|e| GenericError::from(format!("Failed to parse agent config: {}", e)))?;

        // Create the agent
        let mut agent = Agent {
            id: self.id.clone(),
            seq_id: self.seq_id,
            name: self.name.clone(),
            description: self.description.clone(),
            state,
            roles,
            capabilities: std::collections::HashSet::new(), // Will be populated from roles
            created_at: self.created_at,
            last_active: self.last_active,
            model_preference: self.model_preference.clone(),
            template_key: self.template_key.clone(),
            model_metadata: None, // Not stored in DB
            can_communicate_with_humans: self.can_communicate_with_humans,
            config,
        };

        // Populate capabilities from roles
        agent.update_capabilities_from_roles();

        Ok(agent)
    }
}

/// Enhanced registry for managing agents with persistence and advanced lookup
#[derive(Debug)]
pub struct EnhancedAgentRegistry {
    /// In-memory cache of agents for quick access
    agents_cache: Cache<AgentId, Agent>,

    /// Map of agent IDs to sequence IDs
    seq_id_map: Arc<RwLock<HashMap<AgentId, SeqId>>>,

    /// Counter for generating new sequence IDs
    next_seq_id: Arc<RwLock<SeqId>>,

    /// Optional database connection for persistence
    db_connection: Option<Arc<SurrealDbConnection>>,
}

impl EnhancedAgentRegistry {
    /// Creates a new EnhancedAgentRegistry
    pub fn new(db_connection: Option<Arc<SurrealDbConnection>>) -> Self {
        info!("Initializing EnhancedAgentRegistry");
        Self {
            agents_cache: Cache::new(),
            seq_id_map: Arc::new(RwLock::new(HashMap::new())),
            next_seq_id: Arc::new(RwLock::new(1)), // Start from 1
            db_connection,
        }
    }

    /// Loads agents from the database if available
    pub async fn load_from_db(&self) -> PrismaResult<()> {
        if let Some(db) = &self.db_connection {
            info!("Loading agents from database");

            // Query all agents from the database
            let query = "SELECT * FROM agents";
            let records: Vec<AgentRecord> = db.query(query, &[]).await?;

            if records.is_empty() {
                debug!("No agents found in database");
                return Ok(());
            }

            info!("Found {} agent records in database", records.len());

            // Process each agent record
            for record in records {
                match record.to_agent() {
                    Ok(agent) => {
                        // Update the cache
                        self.agents_cache.insert(agent.id.clone(), agent.clone()).await?;

                        // Update the seq_id map
                        let mut map = self.seq_id_map.write().await;
                        map.insert(agent.id.clone(), agent.seq_id);

                        // Update the next_seq_id counter if needed
                        let mut next_id = self.next_seq_id.write().await;
                        if agent.seq_id >= *next_id {
                            let new_next_id = agent.seq_id + 1;
                            // Ensure we don't exceed LLAMA_MAX_SEQ bounds
                            *next_id = if new_next_id >= 64 { 1 } else { new_next_id };
                        }
                    },
                    Err(e) => {
                        warn!("Failed to convert agent record to agent: {}", e);
                    }
                }
            }

            info!("Successfully loaded agents from database");
        } else {
            debug!("No database connection available, skipping agent loading");
        }
        Ok(())
    }

    /// Saves an agent to the database if available
    async fn save_to_db(&self, agent: &Agent) -> PrismaResult<()> {
        if let Some(db) = &self.db_connection {
            debug!("Saving agent {} to database", agent.id);

            // Convert agent to database record
            let record = AgentRecord::from_agent(agent);

            // Check if the agent already exists
            let exists_query = "SELECT COUNT(*) as count FROM agents WHERE id = $1";
            let result: Vec<serde_json::Value> = db.query(exists_query, &[("1", &agent.id)]).await?;

            let exists = if let Some(value) = result.first() {
                if let Some(count) = value.get("count") {
                    count.as_i64().unwrap_or(0) > 0
                } else {
                    false
                }
            } else {
                false
            };

            if exists {
                // Update existing agent
                debug!("Updating existing agent {} in database", agent.id);
                let update_query = "UPDATE agents SET * = $1 WHERE id = $2";
                let record_json = serde_json::to_string(&record).map_err(|e| GenericError::from(format!("Failed to serialize record: {}", e)))?;
                db.query::<serde_json::Value>(update_query, &[("1", &record_json), ("2", &agent.id)]).await?;
            } else {
                // Create new agent
                debug!("Creating new agent {} in database", agent.id);
                let create_query = "CREATE agents CONTENT $1";
                let record_json = serde_json::to_string(&record).map_err(|e| GenericError::from(format!("Failed to serialize record: {}", e)))?;
                db.query::<serde_json::Value>(create_query, &[("1", &record_json)]).await?;
            }

            debug!("Agent {} saved to database", agent.id);
        } else {
            debug!("No database connection available, skipping agent saving");
        }
        Ok(())
    }

    /// Gets the next available sequence ID
    /// Ensures sequence IDs stay within LLAMA_MAX_SEQ bounds (0-63)
    async fn get_next_seq_id(&self) -> SeqId {
        let mut next_id = self.next_seq_id.write().await;
        let id = *next_id;
        *next_id += 1;

        // Wrap around if we exceed LLAMA_MAX_SEQ (64)
        // Keep range 1-63 to avoid using 0 which might be reserved
        if *next_id >= 64 {
            *next_id = 1;
        }

        id
    }

    /// Creates a new agent with the given ID and role
    pub async fn create_agent(&self, agent_id: AgentId, name: String, role: AgentRole) -> PrismaResult<Agent> {
        // Check if agent already exists
        if let Some(agent) = self.get_agent(&agent_id).await? {
            return Ok(agent);
        }

        // Get a new sequence ID
        let seq_id = self.get_next_seq_id().await;

        // Create the new agent
        let agent = Agent::new(agent_id.clone(), seq_id, name, role);

        // Save to cache
        self.agents_cache.insert(agent_id.clone(), agent.clone()).await?;

        // Update seq_id map
        {
            let mut map = self.seq_id_map.write().await;
            map.insert(agent_id.clone(), seq_id);
        }

        // Save to database
        self.save_to_db(&agent).await?;

        Ok(agent)
    }

    /// Creates a new agent with the given ID and multiple roles
    pub async fn create_agent_with_roles(&self, agent_id: AgentId, name: String, roles: Vec<AgentRole>) -> PrismaResult<Agent> {
        // Check if agent already exists
        if let Some(agent) = self.get_agent(&agent_id).await? {
            return Ok(agent);
        }

        // Get a new sequence ID
        let seq_id = self.get_next_seq_id().await;

        // Create the new agent with multiple roles
        let agent = Agent::with_roles(agent_id.clone(), seq_id, name, roles);

        // Save to cache
        self.agents_cache.insert(agent_id.clone(), agent.clone()).await?;

        // Update seq_id map
        {
            let mut map = self.seq_id_map.write().await;
            map.insert(agent_id.clone(), seq_id);
        }

        // Save to database
        self.save_to_db(&agent).await?;

        Ok(agent)
    }

    /// Gets agents by role name
    pub async fn get_agents_by_role_name(&self, role_name: &str) -> PrismaResult<Vec<Agent>> {
        let mut result = Vec::new();

        // Get all agents
        let agents = self.get_all_agents().await?;

        // Filter by role name
        for agent in agents {
            if agent.has_role(role_name) {
                result.push(agent);
            }
        }

        Ok(result)
    }

    /// Adds a role to an agent
    pub async fn add_role_to_agent(&self, agent_id: &AgentId, role: AgentRole) -> PrismaResult<()> {
        // Get the agent
        let mut agent = match self.get_agent(agent_id).await? {
            Some(agent) => agent,
            None => return Err(crate::err::GenericError::from(format!("Agent {} not found", agent_id))),
        };

        // Add the role
        agent.add_role(role);

        // Update the agent
        self.update_agent(agent).await
    }

    /// Removes a role from an agent
    pub async fn remove_role_from_agent(&self, agent_id: &AgentId, role_name: &str) -> PrismaResult<bool> {
        // Get the agent
        let mut agent = match self.get_agent(agent_id).await? {
            Some(agent) => agent,
            None => return Err(crate::err::GenericError::from(format!("Agent {} not found", agent_id))),
        };

        // Remove the role
        let result = agent.remove_role(role_name);

        // Update the agent if a role was removed
        if result {
            self.update_agent(agent).await?;
        }

        Ok(result)
    }

    /// Gets all agents
    pub async fn get_all_agents(&self) -> PrismaResult<Vec<Agent>> {
        let agent_ids = self.get_all_agent_ids().await?;
        let mut agents = Vec::new();

        for id in agent_ids {
            if let Some(agent) = self.get_agent(&id).await? {
                agents.push(agent);
            }
        }

        Ok(agents)
    }

    /// Adds an agent to the registry
    pub async fn add_agent(&self, agent: Agent) -> PrismaResult<()> {
        // Add to seq_id map if not already present
        let mut map = self.seq_id_map.write().await;
        if !map.contains_key(&agent.id) {
            map.insert(agent.id.clone(), agent.seq_id);
        }
        drop(map); // Release the write lock

        // Update the agent (which will update cache and database)
        self.update_agent(agent).await
    }
}

#[async_trait::async_trait]
impl AgentRegistry for EnhancedAgentRegistry {
    async fn register_or_get_agent(&self, agent_id: AgentId) -> PrismaResult<SeqId> {
        // Check if agent already exists in seq_id map
        {
            let map = self.seq_id_map.read().await;
            if let Some(seq_id) = map.get(&agent_id) {
                debug!("Agent '{}' already registered with SeqId: {}", agent_id, seq_id);
                return Ok(*seq_id);
            }
        }

        // Agent doesn't exist, create a new one with default role
        let agent = self.create_agent(agent_id.clone(), format!("Agent {}", agent_id), AgentRole::assistant()).await?;
        Ok(agent.seq_id)
    }

    async fn is_agent_registered(&self, agent_id: &AgentId) -> PrismaResult<bool> {
        let map = self.seq_id_map.read().await;
        Ok(map.contains_key(agent_id))
    }

    async fn get_seq_id(&self, agent_id: &AgentId) -> PrismaResult<Option<SeqId>> {
        let map = self.seq_id_map.read().await;
        Ok(map.get(agent_id).copied())
    }

    async fn unregister_agent(&self, agent_id: &AgentId) -> PrismaResult<bool> {
        // Remove from cache
        let cache_result = self.agents_cache.remove(agent_id).await?;

        // Remove from seq_id map
        let mut map = self.seq_id_map.write().await;
        let map_result = map.remove(agent_id).is_some();

        // Remove from database if available
        if let Some(db) = &self.db_connection {
            debug!("Removing agent {} from database", agent_id);

            // Delete the agent from the database
            let delete_query = "DELETE FROM agents WHERE id = $1";
            db.query::<serde_json::Value>(delete_query, &[("1", agent_id)]).await?;

            // Also delete related records (capabilities, messages, etc.)
            let delete_capabilities_query = "DELETE FROM agent_capabilities WHERE agent_id = $1";
            db.query::<serde_json::Value>(delete_capabilities_query, &[("1", agent_id)]).await?;

            let delete_states_query = "DELETE FROM agent_states WHERE agent_id = $1";
            db.query::<serde_json::Value>(delete_states_query, &[("1", agent_id)]).await?;

            debug!("Agent {} and related records removed from database", agent_id);
        }

        Ok(cache_result || map_result)
    }

    async fn get_all_agent_ids(&self) -> PrismaResult<Vec<AgentId>> {
        let map = self.seq_id_map.read().await;
        Ok(map.keys().cloned().collect())
    }

    async fn get_agent(&self, agent_id: &AgentId) -> PrismaResult<Option<Agent>> {
        // Try to get from cache first
        if let Some(agent) = self.agents_cache.get(agent_id).await {
            return Ok(Some(agent));
        }

        // If not in cache but in seq_id map, try to load from database
        let in_map = {
            let map = self.seq_id_map.read().await;
            map.contains_key(agent_id)
        };

        if in_map {
            if let Some(db) = &self.db_connection {
                debug!("Loading agent {} from database", agent_id);

                // Query the agent from the database
                let query = "SELECT * FROM agents WHERE id = $1";
                let records: Vec<AgentRecord> = db.query(query, &[("1", agent_id)]).await?;

                if let Some(record) = records.first() {
                    match record.to_agent() {
                        Ok(agent) => {
                            // Cache the agent for future use
                            self.agents_cache.insert(agent_id.clone(), agent.clone()).await?;
                            return Ok(Some(agent));
                        },
                        Err(e) => {
                            warn!("Failed to convert agent record to agent: {}", e);
                        }
                    }
                } else {
                    debug!("Agent {} not found in database", agent_id);
                }
            }
        }

        Ok(None)
    }

    async fn update_agent(&self, agent: Agent) -> PrismaResult<()> {
        // Update cache
        self.agents_cache.insert(agent.id.clone(), agent.clone()).await?;

        // Update database if available
        self.save_to_db(&agent).await?;

        Ok(())
    }
}
