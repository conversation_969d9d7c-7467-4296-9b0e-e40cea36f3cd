use async_trait::async_trait;
use std::sync::Arc;
use std::any::Any;
use tokio::sync::{RwLock, oneshot, Mutex};
use tracing::{info, error, warn};

use crate::err::{PrismaResult, types::PrismaError, types::generics::EngineOperationError};
use super::agent_manager::AgentManager; // Added import for AgentManager
use super::types::{
    EngineConfig, EngineStatus, TaskId, EngineErrorType, ExecutionStrategyType
};
// Use the LlmService trait defined in the tcl module
use crate::prisma::prisma_engine::tcl::llm_task::LlmService;
use crate::llm::implementation::service::LlmServiceImpl; // Import concrete LlmServiceImpl
// Import TaskFactory from tcl module
use crate::prisma::prisma_engine::tcl::TaskFactory;
use crate::prisma::prisma_engine::tcl::embedding_task::LlmEmbeddingService;

// --- Existing code ---
// Removed EngineOperationError definition (moved to err/types/generics.rs)
use super::traits::{
    EngineController, Task, ResourceMonitor, DecisionLogic, Executor
};
// Import the concrete types we'll instantiate
use super::monitor::{Monitor, types::MonitorConfig};
use super::decision_maker::{RuleBasedDecisionMaker, types::DecisionMakerConfig};
use super::executor::{TaskExecutor, types::ExecutorConfig};
// Import execution strategies
use super::execution_strategies::{ExecutionStrategies, types::ExecutionStrategyConfig};
// Import storage components
use crate::storage::{SurrealDbConnection, DatabaseConnection};

// The main struct for the Prisma Engine
pub struct PrismaEngine {
    config: Arc<RwLock<EngineConfig>>,
    status: Arc<RwLock<EngineStatus>>,
    // Store concrete types wrapped in Arc/RwLock for shared mutability
    monitor: Option<Arc<RwLock<Monitor>>>,
    decision_maker: Option<Arc<RwLock<RuleBasedDecisionMaker>>>,
    executor: Option<Arc<RwLock<TaskExecutor>>>,
    // Add ExecutionStrategies for task execution strategy management
    execution_strategies: Option<Arc<RwLock<ExecutionStrategies>>>,
    storage_service: Option<Arc<SurrealDbConnection>>, // Use concrete type
    agent_manager: Option<Arc<RwLock<AgentManager>>>, // Added AgentManager field
    // Add handle for LLM service
    llm_service: Option<Arc<dyn LlmService + Send + Sync>>, // Use trait object
    // Add handle for embedding service
    embedding_service: Option<Arc<Mutex<dyn LlmEmbeddingService + Send + Sync>>>, // Use trait object
    // Add TaskFactory for creating tasks
    task_factory: Option<Arc<TaskFactory>>,
}

// Manual Debug implementation for PrismaEngine
impl std::fmt::Debug for PrismaEngine {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("PrismaEngine")
         .field("config", &self.config) // Assuming EngineConfig derives Debug
         .field("status", &self.status) // Assuming EngineStatus derives Debug
         .field("monitor", &self.monitor.as_ref().map(|_| "Monitor")) // Indicate presence
         .field("decision_maker", &self.decision_maker.as_ref().map(|_| "DecisionMaker")) // Indicate presence
         .field("executor", &self.executor.as_ref().map(|_| "Executor")) // Indicate presence
         .field("execution_strategies", &self.execution_strategies.as_ref().map(|_| "ExecutionStrategies")) // Indicate presence
         .field("storage_service", &self.storage_service.as_ref().map(|_| "StorageService")) // Indicate presence
         .field("agent_manager", &self.agent_manager.as_ref().map(|_| "AgentManager")) // Indicate presence
         .field("llm_service", &self.llm_service.as_ref().map(|_| "LlmService")) // Indicate presence
         .field("embedding_service", &self.embedding_service.as_ref().map(|_| "EmbeddingService")) // Indicate presence
         .field("task_factory", &self.task_factory.as_ref().map(|_| "TaskFactory")) // Indicate presence
         .finish()
    }
}

impl PrismaEngine {
    /// Creates a new, uninitialized PrismaEngine.
    pub fn new() -> Self {
        info!("Creating new PrismaEngine instance.");
        PrismaEngine {
            config: Arc::new(RwLock::new(EngineConfig::default())),
            status: Arc::new(RwLock::new(EngineStatus::Stopped)),
            monitor: None,
            decision_maker: None,
            executor: None,
            execution_strategies: None, // Initialize ExecutionStrategies as None
            storage_service: None,
            agent_manager: None, // Initialize AgentManager as None
            llm_service: None, // Initialize llm_service as None
            embedding_service: None, // Initialize embedding_service as None
            task_factory: None, // Initialize task_factory as None
        }
    }

    // Helper to update status, ensuring thread safety
    async fn set_status(&self, new_status: EngineStatus) {
        let mut status = self.status.write().await;
        *status = new_status;
        info!("PrismaEngine status changed to: {:?}", new_status);
    }

    // Helper to get status
    async fn get_current_status(&self) -> EngineStatus {
        *self.status.read().await
    }

    // --- Public Accessors for Engine Components ---

    /// Returns a shared handle to the AgentManager, if initialized.
    pub fn get_agent_manager(&self) -> Option<Arc<RwLock<AgentManager>>> {
        self.agent_manager.clone()
    }

    /// Returns a shared handle to the StorageService, if initialized.
    pub fn get_storage_service(&self) -> Option<Arc<crate::storage::surrealdb::SurrealDbConnection>> {
        self.storage_service.clone()
    }

    /// Returns a shared handle to the LLM Service, if initialized.
    ///
    /// # Arguments
    /// * `model_id` - The ID of the model to use. This can be:
    ///   - A specific model name (e.g., "Meta-Llama-3-8B-Instruct.Q8_0.gguf")
    ///   - A model size (e.g., "8B", "70B")
    ///   - A model family (e.g., "llama", "mistral")
    ///   - "default" to use the default model
    ///
    /// If the requested model is not loaded, this will return None.
    /// In the future, this could be enhanced to dynamically load models on demand.
    pub fn get_llm_service(&self, model_id: &str) -> Option<Arc<dyn LlmService + Send + Sync>> {
        info!("Requested LLM service for model: {}", model_id);

        // Get the current configuration
        let config = match futures::executor::block_on(self.config.read()) {
            cfg => cfg,
        };

        // Get the configured model path
        let configured_model_path = match &config.llm_config {
            Some(cfg) => &cfg.model_path,
            None => "models/Meta-Llama-3-8B-Instruct.Q8_0.gguf", // Default model path
        };

        // Extract the model name from the path
        let configured_model_name = configured_model_path
            .split('/')
            .last()
            .unwrap_or("unknown");

        // Check if the requested model matches the loaded model
        let model_matches = match model_id.to_lowercase().as_str() {
            "default" => true, // Default always matches the loaded model
            id if id == configured_model_name.to_lowercase() => true, // Exact match
            id if configured_model_name.to_lowercase().contains(&id) => true, // Partial match (e.g., "llama" matches "Meta-Llama-3-8B")
            _ => false, // No match
        };

        if model_matches {
            info!("Using loaded model '{}' for request '{}'", configured_model_name, model_id);
            self.llm_service.clone()
        } else {
            warn!("Requested model '{}' does not match loaded model '{}'. Model switching not yet implemented.",
                  model_id, configured_model_name);
            // For now, return the loaded model anyway as a fallback
            // In the future, this could dynamically load the requested model
            self.llm_service.clone()
        }
    }

    /// Returns a shared handle to the Embedding Service, if initialized.
    ///
    /// # Arguments
    /// * `model_id` - The ID of the model to use. This can be:
    ///   - A specific model name (e.g., "all-MiniLM-L6-v2.Q4_0.gguf")
    ///   - A model size (e.g., "L6", "L12")
    ///   - A model family (e.g., "minilm", "e5")
    ///   - "default" to use the default model
    ///
    /// If the requested model is not loaded, this will return None.
    /// In the future, this could be enhanced to dynamically load models on demand.
    pub fn get_embedding_service(&self, model_id: &str) -> Option<Arc<Mutex<dyn LlmEmbeddingService + Send + Sync>>> {
        info!("Requested embedding service for model: {}", model_id);

        // Get the current configuration
        let config = match futures::executor::block_on(self.config.read()) {
            cfg => cfg,
        };

        // Get the configured model path
        let configured_model_path = match &config.embedding_config {
            Some(cfg) => &cfg.model_path,
            None => "models/all-MiniLM-L6-v2.Q4_0.gguf", // Default model path
        };

        // Extract the model name from the path
        let configured_model_name = configured_model_path
            .split('/')
            .last()
            .unwrap_or("unknown");

        // Check if the requested model matches the loaded model
        let model_matches = match model_id.to_lowercase().as_str() {
            "default" => true, // Default always matches the loaded model
            id if id == configured_model_name.to_lowercase() => true, // Exact match
            id if configured_model_name.to_lowercase().contains(&id) => true, // Partial match (e.g., "minilm" matches "all-MiniLM-L6")
            _ => false, // No match
        };

        if model_matches {
            info!("Using loaded embedding model '{}' for request '{}'", configured_model_name, model_id);
            self.embedding_service.clone()
        } else {
            warn!("Requested embedding model '{}' does not match loaded model '{}'. Model switching not yet implemented.",
                  model_id, configured_model_name);
            // For now, return the loaded model anyway as a fallback
            // In the future, this could dynamically load the requested model
            self.embedding_service.clone()
        }
    }

    /// Returns a shared handle to the Task Factory, if initialized.
    /// The Task Factory is used to create tasks of different types.
    pub fn get_task_factory(&self) -> Option<Arc<TaskFactory>> {
        info!("Requested Task Factory");
        self.task_factory.clone()
    }

    /// Returns a shared handle to the ExecutionStrategies, if initialized.
    /// The ExecutionStrategies component is used to execute tasks using different strategies.
    pub fn get_execution_strategies(&self) -> Option<Arc<RwLock<ExecutionStrategies>>> {
        info!("Requested ExecutionStrategies");
        self.execution_strategies.clone()
    }

    /// Sets the AgentManager for testing purposes.
    /// This method should only be used in tests.
    pub fn set_agent_manager_for_testing(&mut self, agent_manager: AgentManager) {
        info!("Setting AgentManager for testing");
        self.agent_manager = Some(Arc::new(RwLock::new(agent_manager)));
    }

    /// Sets the StorageService for testing purposes.
    /// This method should only be used in tests.
    pub fn set_storage_service_for_testing(&mut self, storage_service: crate::storage::surrealdb::SurrealDbConnection)
    {
        info!("Setting StorageService for testing");
        self.storage_service = Some(Arc::new(storage_service));
    }

    /// Sets the ExecutionStrategies for testing purposes.
    /// This method should only be used in tests.
    pub fn set_execution_strategies_for_testing(&mut self, execution_strategies: ExecutionStrategies) {
        info!("Setting ExecutionStrategies for testing");
        self.execution_strategies = Some(Arc::new(RwLock::new(execution_strategies)));
    }
}

#[async_trait]
impl EngineController for PrismaEngine {
    /// Initialize the engine with configuration.
    async fn initialize(&mut self, config: EngineConfig) -> PrismaResult<()> {
        info!("Initializing PrismaEngine...");
        let mut status = self.status.write().await;
        if *status != EngineStatus::Stopped {
            warn!("Engine cannot be initialized while not in Stopped state. Current state: {:?}", *status);
            // Wrap the string in EngineOperationError
            return Err(PrismaError::new(EngineOperationError::new("Engine already initialized or running".to_string()))); // Already fixed, but keeping for context
        }
        *status = EngineStatus::Initializing;
        drop(status);

        // Store the provided config
        {
            let mut cfg_write = self.config.write().await;
            *cfg_write = config.clone();
        }

        // --- Initialize Storage Service ---
        // Extract storage configuration from EngineConfig
        let storage_config = match &config.storage_config {
            Some(cfg) => cfg.clone(),
            None => {
                info!("No storage configuration provided, using defaults");
                super::types::StorageConfig::default()
            }
        };

        // Determine connection string based on configuration
        let connection_string = if storage_config.use_http {
            format!("http://{}", storage_config.connection_string.split("://").nth(1).unwrap_or("localhost:8000"))
        } else {
            format!("ws://{}", storage_config.connection_string.split("://").nth(1).unwrap_or("localhost:8000"))
        };

        info!("Connecting to SurrealDB at {}", connection_string);

        match SurrealDbConnection::connect(&connection_string).await {
            Ok(conn) => {
                // Sign in and select namespace/db
                let username = &storage_config.username;
                let password = &storage_config.password;
                let namespace = &storage_config.namespace;
                let database = &storage_config.database;

                info!("Authenticating with SurrealDB as user: {}, namespace: {}, database: {}",
                      username, namespace, database);

                // Authenticate and select namespace/database using the connection
                if let Err(e) = conn.signin(surrealdb::opt::auth::Root {
                    username,
                    password,
                }).await {
                    let err_msg = format!("Failed to authenticate with SurrealDB: {}", e);
                    error!("{}", err_msg);
                    self.set_status(EngineStatus::Error(EngineErrorType::InitializationFailed)).await;
                    return Err(PrismaError::new(EngineOperationError::new(err_msg)));
                }

                // Select namespace and database
                if let Err(e) = conn.use_ns(namespace).await {
                    let err_msg = format!("Failed to select namespace '{}': {}", namespace, e);
                    error!("{}", err_msg);
                    self.set_status(EngineStatus::Error(EngineErrorType::InitializationFailed)).await;
                    return Err(PrismaError::new(EngineOperationError::new(err_msg)));
                }

                if let Err(e) = conn.use_db(database).await {
                    let err_msg = format!("Failed to select database '{}': {}", database, e);
                    error!("{}", err_msg);
                    self.set_status(EngineStatus::Error(EngineErrorType::InitializationFailed)).await;
                    return Err(PrismaError::new(EngineOperationError::new(err_msg)));
                }

                // Store the connection
                self.storage_service = Some(Arc::new(conn));
                info!("StorageService (SurrealDbConnection) initialized successfully");
            }
            Err(e) => {
                let err_msg = format!("Failed to connect to SurrealDB at {}: {}", connection_string, e);
                error!("{}", err_msg);
                self.set_status(EngineStatus::Error(EngineErrorType::InitializationFailed)).await;
                return Err(PrismaError::new(EngineOperationError::new(err_msg)));
            }
        }


        // --- Initialize Monitor ---
        // Extract monitor configuration from EngineConfig
        let monitor_config = match &config.monitor_config {
            Some(cfg) => {
                info!("Using provided monitor configuration");
                MonitorConfig {
                    poll_interval_ms: cfg.system_monitor_interval_ms,
                    // Add other fields from cfg as needed
                }
            },
            None => {
                info!("No monitor configuration provided, using defaults with engine interval");
                MonitorConfig {
                    poll_interval_ms: config.monitor_interval_ms,
                    // Use default values for other fields
                }
            }
        };

        info!("Initializing Monitor with poll interval: {} ms", monitor_config.poll_interval_ms);
        let monitor_impl = Monitor::new(monitor_config);
        self.monitor = Some(Arc::new(RwLock::new(monitor_impl)));
        info!("Monitor initialized successfully");

        // --- Initialize Decision Maker ---
        // Extract decision maker configuration from EngineConfig
        let dm_config_from_engine = match &config.decision_maker_config {
            Some(cfg) => {
                info!("Using provided decision maker configuration");
                cfg.clone()
            },
            None => {
                info!("No decision maker configuration provided, using defaults");
                super::types::DecisionMakerConfig::default()
            }
        };

        // Convert to the decision maker's expected config type
        let dm_config = crate::prisma::prisma_engine::decision_maker::types::DecisionMakerConfig {
            high_cpu_threshold: dm_config_from_engine.high_cpu_threshold,
            high_memory_threshold: dm_config_from_engine.high_memory_threshold,
            default_strategy: dm_config_from_engine.default_strategy,
            rule_evaluation_timeout_ms: dm_config_from_engine.rule_evaluation_timeout_ms,
            enable_priority_adjustment: dm_config_from_engine.enable_priority_adjustment,
            enable_rule_based_decisions: dm_config_from_engine.enable_rule_based_decisions,
            enable_state_tracking: dm_config_from_engine.enable_state_tracking,
            category_strategies: std::collections::HashMap::new(), // Initialize with defaults
            resource_thresholds: std::collections::HashMap::new(), // Initialize with defaults
        };

        info!("Initializing RuleBasedDecisionMaker with default strategy: {:?}", dm_config.default_strategy);
        let decision_impl = RuleBasedDecisionMaker::new(dm_config);
        self.decision_maker = Some(Arc::new(RwLock::new(decision_impl)));
        info!("RuleBasedDecisionMaker initialized successfully");

        // --- Initialize ExecutionStrategies ---
        // Extract execution strategy configuration from EngineConfig
        let exec_strategy_config = match &config.execution_strategy_config {
            Some(cfg) => {
                info!("Using provided execution strategy configuration");
                cfg.clone()
            },
            None => {
                info!("No execution strategy configuration provided, using defaults");
                super::types::ExecutionStrategyConfig::default()
            }
        };

        // Create a map of strategy configs
        let mut strategy_configs = std::collections::HashMap::new();

        // Add Direct strategy config
        let direct_config = ExecutionStrategyConfig {
            strategy_type: ExecutionStrategyType::Direct,
            max_concurrent_tasks: exec_strategy_config.max_concurrent_direct_tasks,
            task_timeout: Some(std::time::Duration::from_millis(exec_strategy_config.task_timeout_ms)),
            detailed_logging: exec_strategy_config.enable_detailed_logging,
            strategy_specific_json: None,
        };
        strategy_configs.insert(ExecutionStrategyType::Direct, direct_config);
        info!("Configured Direct strategy with max_concurrent_tasks: {}", exec_strategy_config.max_concurrent_direct_tasks);

        // Add Rayon strategy config
        let rayon_config = ExecutionStrategyConfig {
            strategy_type: ExecutionStrategyType::Rayon,
            max_concurrent_tasks: exec_strategy_config.max_concurrent_rayon_tasks,
            task_timeout: Some(std::time::Duration::from_millis(exec_strategy_config.task_timeout_ms)),
            detailed_logging: exec_strategy_config.enable_detailed_logging,
            strategy_specific_json: None,
        };
        strategy_configs.insert(ExecutionStrategyType::Rayon, rayon_config);
        info!("Configured Rayon strategy with max_concurrent_tasks: {}", exec_strategy_config.max_concurrent_rayon_tasks);

        // Add Tokio strategy config
        let tokio_config = ExecutionStrategyConfig {
            strategy_type: ExecutionStrategyType::Tokio,
            max_concurrent_tasks: exec_strategy_config.max_concurrent_tokio_tasks,
            task_timeout: Some(std::time::Duration::from_millis(exec_strategy_config.task_timeout_ms)),
            detailed_logging: exec_strategy_config.enable_detailed_logging,
            strategy_specific_json: None,
        };
        strategy_configs.insert(ExecutionStrategyType::Tokio, tokio_config);
        info!("Configured Tokio strategy with max_concurrent_tasks: {}", exec_strategy_config.max_concurrent_tokio_tasks);

        // Create ExecutionStrategies with configs
        let execution_strategies_impl = ExecutionStrategies::with_configs(strategy_configs);
        self.execution_strategies = Some(Arc::new(RwLock::new(execution_strategies_impl)));
        info!("ExecutionStrategies initialized successfully with default strategy: {:?}", exec_strategy_config.default_strategy);

        // --- Initialize Executor ---
        // Extract executor configuration from EngineConfig
        let exec_config_from_engine = match &config.executor_config {
            Some(cfg) => {
                info!("Using provided executor configuration");
                cfg.clone()
            },
            None => {
                info!("No executor configuration provided, using defaults");
                super::types::ExecutorConfig::default()
            }
        };

        // Convert to the executor's expected config type
        let exec_config = crate::prisma::prisma_engine::executor::ExecutorConfig {
            tokio_worker_threads: exec_config_from_engine.tokio_worker_threads,
            rayon_worker_threads: exec_config_from_engine.rayon_worker_threads,
            realtime_queue_capacity: exec_config_from_engine.realtime_queue_capacity,
            high_priority_queue_capacity: exec_config_from_engine.high_priority_queue_capacity,
            normal_priority_queue_capacity: exec_config_from_engine.normal_priority_queue_capacity,
            low_priority_queue_capacity: exec_config_from_engine.low_priority_queue_capacity,
        };

        info!("Initializing TaskExecutor with queue capacities: realtime={}, high={}, normal={}, low={}",
              exec_config.realtime_queue_capacity,
              exec_config.high_priority_queue_capacity,
              exec_config.normal_priority_queue_capacity,
              exec_config.low_priority_queue_capacity);

        let mut executor_impl = TaskExecutor::new(exec_config, Arc::clone(&self.config));

        match executor_impl.initialize(&config).await {
            Ok(_) => {
                self.executor = Some(Arc::new(RwLock::new(executor_impl)));
                info!("TaskExecutor initialized successfully");
            },
            Err(e) => {
                let err_msg = format!("Failed to initialize TaskExecutor: {}", e);
                error!("{}", err_msg);
                self.set_status(EngineStatus::Error(EngineErrorType::ExecutorFailed)).await;
                return Err(PrismaError::new(EngineOperationError::new(err_msg)));
            }
        }

        // --- Initialize Agent Manager ---
        // Extract agent manager configuration from EngineConfig
        let agent_manager_config = match &config.agent_manager_config {
            Some(cfg) => {
                info!("Using provided agent manager configuration");
                cfg.clone()
            },
            None => {
                info!("No agent manager configuration provided, using defaults");
                super::types::AgentManagerConfig::default()
            }
        };

        info!("Initializing AgentManager with max_agents: {}, memory capacity: {}",
              agent_manager_config.max_agents,
              agent_manager_config.short_term_memory_capacity);

        // Check if we have the storage service available
        if let Some(storage_service) = &self.storage_service {
            // Create the AgentManager with the storage service in enhanced mode
            let agent_manager_impl = AgentManager::new_enhanced(
                Some(storage_service.clone()),
                None, // No YamlConfigManager for now
                None, // Use default pm.toml path
                None  // Use default prompt_template.toml path
            );
            self.agent_manager = Some(Arc::new(RwLock::new(agent_manager_impl)));
            info!("AgentManager initialized successfully with storage service in enhanced mode");
        } else {
            // Create the AgentManager in basic mode
            let agent_manager_impl = AgentManager::new();
            self.agent_manager = Some(Arc::new(RwLock::new(agent_manager_impl)));
            warn!("AgentManager initialized in basic mode without storage service - some features may be limited");
        }

        // --- Initialize LLM Service ---
        // Extract LLM configuration from EngineConfig
        let llm_config = match &config.llm_config {
            Some(cfg) => {
                info!("Using provided LLM configuration");
                cfg.clone()
            },
            None => {
                info!("No LLM configuration provided, using defaults");
                super::types::LlmConfig::default()
            }
        };

        // Get the model path from the configuration
        let model_path = if llm_config.model_path.starts_with("/") {
            // Absolute path
            llm_config.model_path.clone()
        } else {
            // Relative path - prepend the model directory
            format!("/Users/<USER>/Documents/prisma_workspace/{}", llm_config.model_path)
        };

        info!("Initializing LLM service with model: {}", model_path);

        // Create model parameters from configuration - use default and modify only what we need
        use crate::ModelParams;
        let mut model_params = ModelParams::default();
        model_params.n_gpu_layers = 0; // CPU-only as specified
        model_params.use_mmap = true;
        model_params.use_mlock = false;
        let model_params = Some(model_params);

        // Create context parameters from configuration - use default and modify only what we need
        use crate::ContextParams;
        let mut context_params = ContextParams::default();
        context_params.n_ctx = llm_config.context_size;
        context_params.n_batch = llm_config.batch_size;
        context_params.n_threads = llm_config.threads;
        context_params.n_threads_batch = llm_config.threads;
        context_params.logits_all = true;
        let context_params = Some(context_params);

        match LlmServiceImpl::new(&model_path, model_params, context_params) {
            Ok(llm_instance) => {
                self.llm_service = Some(Arc::new(llm_instance));
                info!("LlmService initialized successfully with model: {}", model_path);
            }
            Err(e) => {
                let err_msg = format!("Failed to initialize LlmService with model {}: {}", model_path, e);
                error!("{}", err_msg);
                self.set_status(EngineStatus::Error(EngineErrorType::InitializationFailed)).await;
                return Err(PrismaError::new(EngineOperationError::new(err_msg)));
            }
        }

        // --- Initialize Embedding Service ---
        // Extract embedding configuration from EngineConfig
        let embedding_config = match &config.embedding_config {
            Some(cfg) => {
                info!("Using provided embedding configuration");
                cfg.clone()
            },
            None => {
                info!("No embedding configuration provided, using defaults");
                super::types::EmbeddingConfig::default()
            }
        };

        // Check if we should use a dedicated embedding model or reuse the LLM service
        if embedding_config.model_path != llm_config.model_path && !embedding_config.model_path.is_empty() {
            // Use a dedicated embedding model
            info!("Initializing dedicated embedding model: {}", embedding_config.model_path);

            // Get the model path from the configuration
            let embedding_model_path = if embedding_config.model_path.starts_with("/") {
                // Absolute path
                embedding_config.model_path.clone()
            } else {
                // Relative path - prepend the model directory
                format!("/Users/<USER>/Documents/prisma_workspace/{}", embedding_config.model_path)
            };

            // Create model parameters for embedding model
            let mut embedding_model_params = ModelParams::default();
            embedding_model_params.n_gpu_layers = 0; // CPU-only as specified
            embedding_model_params.use_mmap = true;
            embedding_model_params.use_mlock = false;

            // Create context parameters for embedding model
            let mut embedding_context_params = ContextParams::default();
            embedding_context_params.n_ctx = embedding_config.embedding_size;
            embedding_context_params.n_batch = embedding_config.batch_size;
            embedding_context_params.n_threads = embedding_config.threads;
            embedding_context_params.n_threads_batch = embedding_config.threads;
            embedding_context_params.embeddings = true; // Enable embeddings

            // Try to load a dedicated embedding model
            // Since DedicatedEmbeddingServiceImpl doesn't exist yet, we'll use the LlmServiceImpl
            // and configure it for embeddings
            use crate::llm::implementation::service::LlmServiceImpl;
            match LlmServiceImpl::new(
                &embedding_model_path,
                Some(embedding_model_params),
                Some(embedding_context_params)
            ) {
                Ok(llm_instance) => {
                    // Create an embedding service wrapper around the LLM service
                    use crate::llm::implementation::embedding::EmbeddingServiceImpl;
                    let embedding_instance = EmbeddingServiceImpl::new(Arc::new(llm_instance));
                    self.embedding_service = Some(Arc::new(Mutex::new(embedding_instance)));
                    info!("Dedicated EmbeddingService initialized successfully with model: {}", embedding_model_path);
                },
                Err(e) => {
                    let err_msg = format!("Failed to initialize dedicated embedding model {}: {}",
                                         embedding_model_path, e);
                    error!("{}", err_msg);
                    warn!("Falling back to using LLM service for embeddings");

                    // Fall back to using the LLM service for embeddings
                    if let Some(llm_service) = &self.llm_service {
                        use crate::llm::implementation::embedding::EmbeddingServiceImpl;
                        let embedding_instance = EmbeddingServiceImpl::new(llm_service.clone());
                        self.embedding_service = Some(Arc::new(Mutex::new(embedding_instance)));
                        info!("EmbeddingService initialized successfully using LLM service as fallback");
                    } else {
                        let err_msg = "Cannot initialize EmbeddingService without LlmService";
                        error!("{}", err_msg);
                        self.set_status(EngineStatus::Error(EngineErrorType::InitializationFailed)).await;
                        return Err(PrismaError::new(EngineOperationError::new(err_msg.to_string())));
                    }
                }
            }
        } else {
            // Use the LLM service for embeddings
            info!("Using LLM service for embeddings");
            if let Some(llm_service) = &self.llm_service {
                use crate::llm::implementation::embedding::EmbeddingServiceImpl;
                let embedding_instance = EmbeddingServiceImpl::new(llm_service.clone());
                self.embedding_service = Some(Arc::new(Mutex::new(embedding_instance)));
                info!("EmbeddingService initialized successfully using LLM service");
            } else {
                let err_msg = "Cannot initialize EmbeddingService without LlmService";
                error!("{}", err_msg);
                self.set_status(EngineStatus::Error(EngineErrorType::InitializationFailed)).await;
                return Err(PrismaError::new(EngineOperationError::new(err_msg.to_string())));
            }
        }

        // --- Initialize Task Factory ---
        if let (Some(llm_service), Some(embedding_service), Some(storage_service)) =
            (&self.llm_service, &self.embedding_service, &self.storage_service) {
            // Create the TaskFactory with the initialized services
            let task_factory = TaskFactory::new(
                llm_service.clone(),
                embedding_service.clone(),
                storage_service.clone()
            );
            self.task_factory = Some(Arc::new(task_factory));
            info!("TaskFactory initialized successfully.");
        } else {
            let err_msg = "Cannot initialize TaskFactory without required services";
            error!("{}", err_msg);
            self.set_status(EngineStatus::Error(EngineErrorType::InitializationFailed)).await;
            return Err(PrismaError::new(EngineOperationError::new(err_msg.to_string())));
        }


        self.set_status(EngineStatus::Stopped).await;
        info!("PrismaEngine initialized successfully.");
        Ok(())
    }

    /// Start the engine and its components.
    async fn start(&mut self) -> PrismaResult<()> {
        info!("Starting PrismaEngine...");
        let current_status = self.get_current_status().await;
        if current_status != EngineStatus::Stopped {
             warn!("Engine cannot be started. Current state: {:?}", current_status);
             // Wrap the string in EngineOperationError
             return Err(PrismaError::new(EngineOperationError::new("Engine not in Stopped state".to_string()))); // Already fixed, but keeping for context
        }

        // Start Monitor
        if let Some(monitor_arc) = &self.monitor {
            let mut monitor = monitor_arc.write().await;
            monitor.start().await?;
            info!("Monitor started.");
        } else {
             warn!("Monitor not initialized. Cannot start.");
             // Decide if this is critical
        }

        // Check if other components are initialized
        if self.executor.is_none() {
             warn!("Executor not initialized. Cannot start.");
             // Wrap the string in EngineOperationError
             return Err(PrismaError::new(EngineOperationError::new("Executor not initialized".to_string()))); // Already fixed, but keeping for context
        }
        if self.execution_strategies.is_none() {
             warn!("ExecutionStrategies not initialized. Cannot start.");
             // Wrap the string in EngineOperationError
             return Err(PrismaError::new(EngineOperationError::new("ExecutionStrategies not initialized".to_string())));
        }
        if self.storage_service.is_none() {
             warn!("StorageService not initialized. Cannot start.");
             // Wrap the string in EngineOperationError
             return Err(PrismaError::new(EngineOperationError::new("StorageService not initialized".to_string()))); // Already fixed, but keeping for context
        }
        if self.llm_service.is_none() {
            warn!("LlmService not initialized. Cannot start.");
            // Wrap the string in EngineOperationError if uncommented
            return Err(PrismaError::new(EngineOperationError::new("LlmService not initialized".to_string())));
        }
        if self.embedding_service.is_none() {
            warn!("EmbeddingService not initialized. Cannot start.");
            return Err(PrismaError::new(EngineOperationError::new("EmbeddingService not initialized".to_string())));
        }
        if self.task_factory.is_none() {
            warn!("TaskFactory not initialized. Cannot start.");
            return Err(PrismaError::new(EngineOperationError::new("TaskFactory not initialized".to_string())));
        }

        info!("All required services are ready.");


        self.set_status(EngineStatus::Running).await;
        info!("PrismaEngine started successfully.");
        Ok(())
    }

    /// Stop the engine gracefully.
    async fn stop(&mut self) -> PrismaResult<()> {
        info!("Stopping PrismaEngine...");
        let current_status = self.get_current_status().await;
        if current_status == EngineStatus::Stopped || current_status == EngineStatus::Stopping {
            warn!("Engine is already stopped or stopping. Current state: {:?}", current_status);
            return Ok(());
        }
        self.set_status(EngineStatus::Stopping).await;

        // Stop Monitor
        if let Some(monitor_arc) = self.monitor.as_ref() { // Use as_ref for read-only access needed by stop
            let mut monitor = monitor_arc.write().await; // Still need write lock to call stop
            if let Err(e) = monitor.stop().await {
                error!("Error stopping Monitor: {:?}", e);
            } else {
                info!("Monitor stopped.");
            }
        }

        // Shutdown Executor
        if let Some(executor_arc) = self.executor.as_ref() { // Use as_ref
             let mut executor = executor_arc.write().await; // Still need write lock to call shutdown
             if let Err(e) = executor.shutdown().await {
                 error!("Error shutting down Executor: {:?}", e);
             } else {
                 info!("Executor stopped.");
             }
        }

        // Reset ExecutionStrategies statistics
        if let Some(exec_strategies_arc) = self.execution_strategies.as_ref() {
            let exec_strategies = exec_strategies_arc.read().await;
            exec_strategies.reset_all_stats().await;
            info!("ExecutionStrategies statistics reset.");
        }

        // Disconnect Storage
        if let Some(_storage_arc) = self.storage_service.as_ref() {
             // Assuming SurrealDbConnection implements DatabaseConnection trait for disconnect
             // Need to downcast Arc<dyn DataStore> or add disconnect to DataStore trait
             // For now, just log.
             info!("StorageService stopping (disconnect logic TBD).");
             // Example if disconnect was on DataStore:
             // if let Err(e) = storage_arc.disconnect().await { // Requires disconnect on DataStore
             //     error!("Error disconnecting StorageService: {:?}", e);
             // } else {
             //     info!("StorageService disconnected.");
             // }
        }

        // Stop/Shutdown LLM Service (if applicable)
        if let Some(_llm_arc) = self.llm_service.as_ref() {
            // Assuming LlmService might have a shutdown/cleanup method in the future
            // For now, just log. Dropping the Arc might be sufficient if it handles cleanup.
            info!("LlmService stopping (shutdown logic TBD).");
            // Example:
            // if let Err(e) = llm_arc.shutdown().await { // Requires shutdown on LlmService trait
            //     error!("Error shutting down LlmService: {:?}", e);
            // } else {
            //     info!("LlmService stopped.");
            // }
        }

        // Stop/Shutdown Embedding Service (if applicable)
        if let Some(_embedding_arc) = self.embedding_service.as_ref() {
            // For now, just log. Dropping the Arc might be sufficient if it handles cleanup.
            info!("EmbeddingService stopping (shutdown logic TBD).");
        }

        // Clear TaskFactory
        if self.task_factory.is_some() {
            self.task_factory = None;
            info!("TaskFactory cleared.");
        }


        self.set_status(EngineStatus::Stopped).await;
        info!("PrismaEngine stopped successfully.");
        Ok(())
    }

    /// Pause the engine's task processing.
    async fn pause(&mut self) -> PrismaResult<()> {
        info!("Pausing PrismaEngine...");
        let current_status = self.get_current_status().await;
        if current_status != EngineStatus::Running {
            warn!("Engine can only be paused when Running. Current state: {:?}", current_status);
            // Wrap the string in EngineOperationError
            return Err(PrismaError::new(EngineOperationError::new("Engine not Running".to_string()))); // Already fixed, but keeping for context
        }

        // Implement pause logic for Executor
        if let Some(executor_arc) = &self.executor {
            let mut executor = executor_arc.write().await;
            match executor.pause().await {
                Ok(_) => {
                    info!("Executor paused successfully");
                },
                Err(e) => {
                    let err_msg = format!("Failed to pause Executor: {}", e);
                    error!("{}", err_msg);
                    return Err(PrismaError::new(EngineOperationError::new(err_msg)));
                }
            }
        } else {
            warn!("Executor not initialized, cannot pause");
        }

        // Pause ExecutionStrategies if available
        if let Some(exec_strategies_arc) = &self.execution_strategies {
            let mut exec_strategies = exec_strategies_arc.write().await;
            match exec_strategies.pause_all().await {
                Ok(_) => {
                    info!("ExecutionStrategies paused successfully");
                },
                Err(e) => {
                    let err_msg = format!("Failed to pause ExecutionStrategies: {}", e);
                    error!("{}", err_msg);
                    // Continue with pausing the engine even if strategies fail to pause
                }
            }
        } else {
            warn!("ExecutionStrategies not initialized, cannot pause");
        }

        self.set_status(EngineStatus::Paused).await;
        info!("PrismaEngine paused.");
        Ok(())
    }

    /// Resume the engine's task processing.
    async fn resume(&mut self) -> PrismaResult<()> {
        info!("Resuming PrismaEngine...");
        let current_status = self.get_current_status().await;
         if current_status != EngineStatus::Paused {
            warn!("Engine can only be resumed when Paused. Current state: {:?}", current_status);
            // Wrap the string in EngineOperationError
            return Err(PrismaError::new(EngineOperationError::new("Engine not Paused".to_string()))); // Already fixed, but keeping for context
        }

        // Implement resume logic for Executor
        if let Some(executor_arc) = &self.executor {
            let mut executor = executor_arc.write().await;
            match executor.resume().await {
                Ok(_) => {
                    info!("Executor resumed successfully");
                },
                Err(e) => {
                    let err_msg = format!("Failed to resume Executor: {}", e);
                    error!("{}", err_msg);
                    return Err(PrismaError::new(EngineOperationError::new(err_msg)));
                }
            }
        } else {
            warn!("Executor not initialized, cannot resume");
        }

        // Resume ExecutionStrategies if available
        if let Some(exec_strategies_arc) = &self.execution_strategies {
            let mut exec_strategies = exec_strategies_arc.write().await;
            match exec_strategies.resume_all().await {
                Ok(_) => {
                    info!("ExecutionStrategies resumed successfully");
                },
                Err(e) => {
                    let err_msg = format!("Failed to resume ExecutionStrategies: {}", e);
                    error!("{}", err_msg);
                    // Continue with resuming the engine even if strategies fail to resume
                }
            }
        } else {
            warn!("ExecutionStrategies not initialized, cannot resume");
        }

        self.set_status(EngineStatus::Running).await;
        info!("PrismaEngine resumed.");
        Ok(())
    }

    /// Submit a new task to the engine.
    async fn submit_task(&self, task: Box<dyn Task>) -> PrismaResult<(TaskId, tokio::sync::oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>)> {
        let task_id = task.id();
        info!("Received task submission: {}", task_id);
        let current_status = self.get_current_status().await;
        if current_status != EngineStatus::Running {
             warn!("Engine not running, cannot submit task {}. Current state: {:?}", task_id, current_status);
             // Wrap the string in EngineOperationError
             return Err(PrismaError::new(EngineOperationError::new("Engine not Running".to_string()))); // Already fixed, but keeping for context
        }

        // --- Task Submission Flow ---
        // 1. Get Task Info
        let task_category = task.category();
        let task_score = task.get_prisma_score();
        let task_priority = task.priority();

        // 2. Get SystemScore from Monitor
        let system_score = match &self.monitor {
            Some(monitor_arc) => monitor_arc.read().await.get_system_score().await?,
            None => {
                let err_msg = format!("Monitor unavailable for task {}", task_id);
                error!("{}", err_msg);
                // Use the new custom error type which implements std::error::Error
                return Err(PrismaError::new(EngineOperationError::new(err_msg))); // No need for full path now
            }
        };

        // 3. Get Strategy from DecisionMaker
        let strategy = match &self.decision_maker {
             Some(dm_arc) => {
                 dm_arc.read().await.decide_strategy(
                     &task_category,
                     &task_score,
                     &system_score,
                     task_priority
                 ).await?
             }
             None => {
                 let err_msg = format!("DecisionMaker unavailable for task {}", task_id);
                 error!("{}", err_msg);
                 return Err(PrismaError::new(EngineOperationError::new(err_msg))); // No need for full path now
             }
        };
        info!("Task {} ({:?}) assigned strategy: {:?}", task_id, task_category, strategy);

        // 4. Check if the task requires a specific LLM or embedding model
        let task_metadata = task.get_metadata();
        let llm_model_id = task_metadata.get("llm_model_id").map(|v| v.as_str().unwrap_or("default"));
        let embedding_model_id = task_metadata.get("embedding_model_id").map(|v| v.as_str().unwrap_or("default"));

        // Log model selection if specified
        if let Some(model_id) = llm_model_id {
            info!("Task {} requests LLM model: {}", task_id, model_id);

            // Check if the requested model is available
            if self.get_llm_service(model_id).is_none() {
                let err_msg = format!("Requested LLM model '{}' is not available for task {}", model_id, task_id);
                warn!("{}", err_msg);
                // Continue with default model
            }
        }

        if let Some(model_id) = embedding_model_id {
            info!("Task {} requests embedding model: {}", task_id, model_id);

            // Check if the requested model is available
            if self.get_embedding_service(model_id).is_none() {
                let err_msg = format!("Requested embedding model '{}' is not available for task {}", model_id, task_id);
                warn!("{}", err_msg);
                // Continue with default model
            }
        }

        // 5. Submit Task using ExecutionStrategies
        if let Some(exec_strategies_arc) = &self.execution_strategies {
            // Create a mutable clone of the task for execution
            let mut task_clone = task.clone_box();

            // Get a read lock on the execution strategies
            let exec_strategies = exec_strategies_arc.read().await;

            // Execute the task using the selected strategy
            let result = exec_strategies.execute_task(&mut *task_clone, strategy).await;

            match result {
                Ok(result_any) => {
                    // Create a oneshot channel to return the result
                    let (sender, receiver) = oneshot::channel();

                    // Send the result through the channel
                    if let Err(_) = sender.send(Ok(result_any)) {
                        let err_msg = format!("Failed to send result for task {}", task_id);
                        error!("{}", err_msg);
                        return Err(PrismaError::new(EngineOperationError::new(err_msg)));
                    }

                    Ok((task_id, receiver))
                },
                Err(e) => {
                    let err_msg = format!("Failed to execute task {}: {}", task_id, e);
                    error!("{}", err_msg);
                    Err(e)
                }
            }
        } else if let Some(exec_arc) = &self.executor {
            // Fall back to using the executor directly if ExecutionStrategies is not available
            warn!("ExecutionStrategies not available, falling back to direct executor for task {}", task_id);
            let exec = exec_arc.read().await;
            exec.submit_task(task, strategy).await
        } else {
            let err_msg = format!("Neither ExecutionStrategies nor Executor available for task {}", task_id);
            error!("{}", err_msg);
            Err(PrismaError::new(EngineOperationError::new(err_msg)))
        }
    }

    /// Get the current status of the engine.
    async fn get_status(&self) -> PrismaResult<EngineStatus> {
        Ok(self.get_current_status().await)
    }
}

// Default implementation for easy creation
impl Default for PrismaEngine {
    fn default() -> Self {
        Self::new()
    }
}
