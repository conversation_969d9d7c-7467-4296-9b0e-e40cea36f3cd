// Import necessary items from standard library and crates
use std::ffi::{CStr, CString, c_char}; // For C string handling
use std::ptr::NonNull; // For safe pointer wrappers
use std::path::Path; // For file path handling

// Import items directly from the crate's ffi module
use crate::ffi::*; // Revert back to wildcard import

// --- Llama struct definition and its impl blocks remain here ---
#[derive(Debug)]
pub struct Llama {
    model: *mut llama_model, // Uses re-exported ffi::llama_model
    ctx: *mut llama_context,   // Uses re-exported ffi::llama_context
}


/// Safe wrapper around llama_model pointer
pub struct Model {
    ptr: NonNull<llama_model>,
}

// Implement Send and Sync for Model since the underlying C library is thread-safe
unsafe impl Send for Model {}
unsafe impl Sync for Model {}

impl Model {
    pub fn as_ptr(&self) -> *mut llama_model {
        self.ptr.as_ptr()
    }
}

/// Safe wrapper around llama_context pointer
pub struct Context {
    ptr: NonNull<llama_context>,
}

// Implement Send and Sync for Context since the underlying C library is thread-safe
unsafe impl Send for Context {}
unsafe impl Sync for Context {}

impl Context {
    pub fn as_ptr(&self) -> *mut llama_context {
        self.ptr.as_ptr()
    }
}

/// Parameters for context creation
#[derive(Debug, Clone)]
pub struct ContextParams {
    /// Size of the context in tokens
    pub n_ctx: usize,
    /// Number of batch tokens to process in parallel
    pub n_batch: usize,
    /// Number of threads to use for generation
    pub n_threads: usize,
    /// Number of threads to use for batch processing
    pub n_threads_batch: usize,
    /// Seed for the random number generator (-1 for random)
    pub seed: i32,
    /// Whether to use memory mapping
    pub use_mmap: bool,
    /// Whether to use memory locking
    pub use_mlock: bool,
    /// Type of embedding pooling to use
    pub pooling_type: PoolingType,
    /// Whether to use f16 for KV cache
    pub f16_kv: bool,
    /// Whether to use logits all
    pub logits_all: bool,
    /// Whether to load only vocabulary
    pub vocab_only: bool,
    /// Whether to use embeddings
    pub embeddings: bool,
}

impl Default for ContextParams {
    fn default() -> Self {
        Self {
            n_ctx: 2048,
            n_batch: 512,
            n_threads: 4,
            n_threads_batch: 4,
            seed: -1,
            use_mmap: true,
            use_mlock: false,
            pooling_type: PoolingType::Mean,
            f16_kv: true,
            logits_all: false,
            vocab_only: false,
            embeddings: false,
        }
    }
}

impl ContextParams {
    /// Convert to C representation
    pub fn into_c_params(self) -> llama_context_params {
        // Start with default parameters to ensure all fields are initialized
        let mut c_params = unsafe { llama_context_default_params() };

        // Override with our custom values
        c_params.n_ctx = self.n_ctx as u32;
        c_params.n_batch = self.n_batch as u32;
        c_params.n_ubatch = self.n_batch as u32; // Set ubatch same as batch for simplicity
        c_params.n_seq_max = 1; // Single sequence for now
        c_params.n_threads = self.n_threads as i32;
        c_params.n_threads_batch = self.n_threads_batch as i32;

        // Set pooling type
        c_params.pooling_type = match self.pooling_type {
            PoolingType::Unspecified => -1, // LLAMA_POOLING_TYPE_UNSPECIFIED
            PoolingType::None => 0, // LLAMA_POOLING_TYPE_NONE
            PoolingType::Mean => 1, // LLAMA_POOLING_TYPE_MEAN
            PoolingType::Cls => 2,  // LLAMA_POOLING_TYPE_CLS
            PoolingType::Last => 3, // LLAMA_POOLING_TYPE_LAST
            PoolingType::Rank => 4, // LLAMA_POOLING_TYPE_RANK
            PoolingType::Unknown(val) => val, // Use the raw value for unknown types
        };

        // Set boolean flags
        c_params.embeddings = self.embeddings;
        c_params.offload_kqv = true; // Enable KQV offloading for better performance
        c_params.flash_attn = false; // Disable flash attention for stability
        c_params.no_perf = false; // Enable performance measurements

        c_params
    }
}

/// Safe wrapper around llama_sampler pointer
pub struct Sampler {
    ptr: NonNull<llama_sampler>,
}

// Implement Send and Sync for Sampler since the underlying C library is thread-safe
unsafe impl Send for Sampler {}
unsafe impl Sync for Sampler {}

impl Sampler {
    pub fn as_ptr(&self) -> *mut llama_sampler {
        self.ptr.as_ptr()
    }
}

/// Position in a sequence
pub type Pos = llama_pos;

/// Token ID
pub type Token = llama_token;

/// Sequence ID
pub type SeqId = llama_seq_id;

/// Type alias for ggml_backend_device pointer
pub type GgmlBackendDev = *mut ggml_backend_device;

/// Safe wrapper around llama_vocab pointer
#[derive(Debug)]
pub struct Vocab {
    ptr: NonNull<llama_vocab>,
}

// Implement Send and Sync for Vocab since the underlying C library is thread-safe
unsafe impl Send for Vocab {}
unsafe impl Sync for Vocab {}

impl Vocab {
    pub fn as_ptr(&self) -> *mut llama_vocab {
        self.ptr.as_ptr()
    }
}

/// Type of vocabulary/tokenizer used by the model
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum VocabType {
    /// No vocabulary
    None,
    /// LLaMA tokenizer based on byte-level BPE with byte fallback
    SPM,
    /// GPT-2 tokenizer based on byte-level BPE
    BPE,
    /// BERT tokenizer based on WordPiece
    WPM,
    /// T5 tokenizer based on Unigram
    UGM,
    /// RWKV tokenizer based on greedy tokenization
    RWKV,
    /// For unrecognized vocab types
    Unknown(u32),
}

// Implementation for converting from u32 to VocabType
impl From<u32> for VocabType {
    fn from(value: u32) -> Self {
        match value {
            0 => VocabType::None,
            1 => VocabType::SPM,
            2 => VocabType::BPE,
            3 => VocabType::WPM,
            4 => VocabType::UGM,
            5 => VocabType::RWKV,
            _ => VocabType::Unknown(value),
        }
    }
}

/// Converts from C llama_vocab_type enum to Rust VocabType enum
///
/// Maps the vocabulary/tokenizer types used by the model:
/// - None: For models without vocab
/// - SPM: LLaMA tokenizer based on byte-level BPE with byte fallback
/// - BPE: GPT-2 tokenizer based on byte-level BPE
/// - WPM: BERT tokenizer based on WordPiece
/// - UGM: T5 tokenizer based on Unigram
/// - RWKV: RWKV tokenizer based on greedy tokenization
/// - Unknown: For unrecognized vocab types
// We don't implement From<llama_vocab_type> for VocabType directly
// because llama_vocab_type is a u32 under the hood, which would conflict
// with our From<u32> implementation. Instead, we'll use a separate function.

/// Convert from llama_vocab_type to VocabType
pub fn vocab_type_from_llama(vocab_type: u32) -> VocabType {
    match vocab_type {
        0 => VocabType::None,
        1 => VocabType::SPM,
        2 => VocabType::BPE,
        3 => VocabType::WPM,
        4 => VocabType::UGM,
        5 => VocabType::RWKV,
        unknown => VocabType::Unknown(unknown),
    }
}

/// Converts from Rust VocabType enum to C llama_vocab_type enum
///
/// Maps vocabulary/tokenizer types back to their C representation:
/// - None -> LLAMA_VOCAB_TYPE_NONE: For models without vocab
/// - Spm -> LLAMA_VOCAB_TYPE_SPM: LLaMA tokenizer based on byte-level BPE with fallback
/// - Bpe -> LLAMA_VOCAB_TYPE_BPE: GPT-2 tokenizer based on byte-level BPE
/// - Wpm -> LLAMA_VOCAB_TYPE_WPM: BERT tokenizer based on WordPiece
/// - Ugm -> LLAMA_VOCAB_TYPE_UGM: T5 tokenizer based on Unigram
/// - Rwkv -> LLAMA_VOCAB_TYPE_RWKV: RWKV tokenizer based on greedy tokenization
/// - Unknown -> Raw value: For unrecognized vocab types
/// Convert from VocabType to llama_vocab_type (which is a u32)
pub fn vocab_type_to_llama(vocab_type: VocabType) -> u32 {
    match vocab_type {
        VocabType::None => 0,
        VocabType::SPM => 1,
        VocabType::BPE => 2,
        VocabType::WPM => 3,
        VocabType::UGM => 4,
        VocabType::RWKV => 5,
        VocabType::Unknown(val) => val,
    }
}

/// Pre-tokenization types for different model architectures
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum VocabPreType {
    Default,
    Llama3,
    DeepseekLlm,
    DeepseekCoder,
    Falcon,
    Mpt,
    Starcoder,
    Gpt2,
    Refact,
    CommandR,
    Stablelm2,
    Qwen2,
    Olmo,
    Dbrx,
    Smaug,
    Poro,
    ChatGlm3,
    ChatGlm4,
    Viking,
    Jais,
    Tekken,
    Smollm,
    Codeshell,
    Bloom,
    Gpt3Finnish,
    Exaone,
    Chameleon,
    Minerva,
    Deepseek3Llm,
    Gpt4o,
    Unknown(u32),
}


/// Converts from C llama_vocab_pre_type enum to Rust VocabPreType enum
///
/// Maps pre-tokenization types for different model architectures:
/// - Default: Standard pre-tokenization
/// - Llama3: LLaMA3 model pre-tokenization
/// - DeepseekLlm: Deepseek LLM pre-tokenization
/// - DeepseekCoder: Deepseek Coder pre-tokenization
/// - Falcon: Falcon model pre-tokenization
/// - Mpt: MPT model pre-tokenization
/// - Starcoder: StarCoder model pre-tokenization
/// - Gpt2: GPT-2 model pre-tokenization
/// - Refact: Refact model pre-tokenization
/// - CommandR: Command-R model pre-tokenization
/// - Stablelm2: StableLM2 model pre-tokenization
/// - Qwen2: Qwen2 model pre-tokenization
/// - Olmo: OLMo model pre-tokenization
/// - Dbrx: DBRX model pre-tokenization
/// - Smaug: SMAUG model pre-tokenization
/// - Poro: PORO model pre-tokenization
/// - ChatGlm3: ChatGLM3 model pre-tokenization
/// - ChatGlm4: ChatGLM4 model pre-tokenization
/// - Viking: Viking model pre-tokenization
/// - Jais: JAIS model pre-tokenization
/// - Tekken: TEKKEN model pre-tokenization
/// - Smollm: SMOLLM model pre-tokenization
/// - Codeshell: CodeShell model pre-tokenization
/// - Bloom: BLOOM model pre-tokenization
/// - Gpt3Finnish: Finnish GPT-3 model pre-tokenization
/// - Exaone: ExaOne model pre-tokenization
/// - Chameleon: Chameleon model pre-tokenization
/// - Minerva: Minerva model pre-tokenization
/// - Deepseek3Llm: Deepseek3 LLM pre-tokenization
/// - Gpt4o: GPT-4O model pre-tokenization
/// - Unknown: For unrecognized pre-tokenization types
/// Convert from u32 to VocabPreType
pub fn vocab_pre_type_from_u32(pre_type: u32) -> VocabPreType {
    match pre_type {
        0 => VocabPreType::Default,
        1 => VocabPreType::Llama3,
        2 => VocabPreType::DeepseekLlm,
        3 => VocabPreType::DeepseekCoder,
        4 => VocabPreType::Falcon,
        5 => VocabPreType::Mpt,
        6 => VocabPreType::Starcoder,
        7 => VocabPreType::Gpt2,
        8 => VocabPreType::Refact,
        9 => VocabPreType::CommandR,
        10 => VocabPreType::Stablelm2,
        11 => VocabPreType::Qwen2,
        12 => VocabPreType::Olmo,
        13 => VocabPreType::Dbrx,
        14 => VocabPreType::Smaug,
        15 => VocabPreType::Poro,
        16 => VocabPreType::ChatGlm3,
        17 => VocabPreType::ChatGlm4,
        18 => VocabPreType::Viking,
        19 => VocabPreType::Jais,
        20 => VocabPreType::Tekken,
        21 => VocabPreType::Smollm,
        22 => VocabPreType::Codeshell,
        23 => VocabPreType::Bloom,
        24 => VocabPreType::Gpt3Finnish,
        25 => VocabPreType::Exaone,
        26 => VocabPreType::Chameleon,
        27 => VocabPreType::Minerva,
        28 => VocabPreType::Deepseek3Llm,
        29 => VocabPreType::Gpt4o,
        unknown => VocabPreType::Unknown(unknown),
    }
}


/// Converts from Rust VocabPreType enum to C llama_vocab_pre_type enum
///
/// Maps pre-tokenization types back to their C representation for different model architectures:
/// - Default -> LLAMA_VOCAB_PRE_TYPE_DEFAULT: Standard pre-tokenization
/// - Llama3 -> LLAMA_VOCAB_PRE_TYPE_LLAMA3: LLaMA3 model pre-tokenization
/// - DeepseekLlm -> LLAMA_VOCAB_PRE_TYPE_DEEPSEEK_LLM: Deepseek LLM pre-tokenization
/// - DeepseekCoder -> LLAMA_VOCAB_PRE_TYPE_DEEPSEEK_CODER: Deepseek Coder pre-tokenization
/// - Falcon -> LLAMA_VOCAB_PRE_TYPE_FALCON: Falcon model pre-tokenization
/// - Mpt -> LLAMA_VOCAB_PRE_TYPE_MPT: MPT model pre-tokenization
/// - Starcoder -> LLAMA_VOCAB_PRE_TYPE_STARCODER: StarCoder model pre-tokenization
/// - Gpt2 -> LLAMA_VOCAB_PRE_TYPE_GPT2: GPT-2 model pre-tokenization
/// - Refact -> LLAMA_VOCAB_PRE_TYPE_REFACT: Refact model pre-tokenization
/// - CommandR -> LLAMA_VOCAB_PRE_TYPE_COMMAND_R: Command-R model pre-tokenization
/// - Stablelm2 -> LLAMA_VOCAB_PRE_TYPE_STABLELM2: StableLM2 model pre-tokenization
/// - Qwen2 -> LLAMA_VOCAB_PRE_TYPE_QWEN2: Qwen2 model pre-tokenization
/// - Olmo -> LLAMA_VOCAB_PRE_TYPE_OLMO: OLMo model pre-tokenization
/// - Dbrx -> LLAMA_VOCAB_PRE_TYPE_DBRX: DBRX model pre-tokenization
/// - Smaug -> LLAMA_VOCAB_PRE_TYPE_SMAUG: SMAUG model pre-tokenization
/// - Poro -> LLAMA_VOCAB_PRE_TYPE_PORO: PORO model pre-tokenization
/// - ChatGlm3 -> LLAMA_VOCAB_PRE_TYPE_CHATGLM3: ChatGLM3 model pre-tokenization
/// - ChatGlm4 -> LLAMA_VOCAB_PRE_TYPE_CHATGLM4: ChatGLM4 model pre-tokenization
/// - Viking -> LLAMA_VOCAB_PRE_TYPE_VIKING: Viking model pre-tokenization
/// - Jais -> LLAMA_VOCAB_PRE_TYPE_JAIS: JAIS model pre-tokenization
/// - Tekken -> LLAMA_VOCAB_PRE_TYPE_TEKKEN: TEKKEN model pre-tokenization
/// - Smollm -> LLAMA_VOCAB_PRE_TYPE_SMOLLM: SMOLLM model pre-tokenization
/// - Codeshell -> LLAMA_VOCAB_PRE_TYPE_CODESHELL: CodeShell model pre-tokenization
/// - Bloom -> LLAMA_VOCAB_PRE_TYPE_BLOOM: BLOOM model pre-tokenization
/// - Gpt3Finnish -> LLAMA_VOCAB_PRE_TYPE_GPT3_FINNISH: Finnish GPT-3 model pre-tokenization
/// - Exaone -> LLAMA_VOCAB_PRE_TYPE_EXAONE: ExaOne model pre-tokenization
/// - Chameleon -> LLAMA_VOCAB_PRE_TYPE_CHAMELEON: Chameleon model pre-tokenization
/// - Minerva -> LLAMA_VOCAB_PRE_TYPE_MINERVA: Minerva model pre-tokenization
/// - Deepseek3Llm -> LLAMA_VOCAB_PRE_TYPE_DEEPSEEK3_LLM: Deepseek3 LLM pre-tokenization
/// - Gpt4o -> LLAMA_VOCAB_PRE_TYPE_GPT4O: GPT-4O model pre-tokenization
/// - Unknown -> Raw value: For unrecognized pre-tokenization types
/// Convert from VocabPreType to u32
pub fn vocab_pre_type_to_u32(pre_type: VocabPreType) -> u32 {
    match pre_type {
        VocabPreType::Default => 0,
        VocabPreType::Llama3 => 1,
        VocabPreType::DeepseekLlm => 2,
        VocabPreType::DeepseekCoder => 3,
        VocabPreType::Falcon => 4,
        VocabPreType::Mpt => 5,
        VocabPreType::Starcoder => 6,
        VocabPreType::Gpt2 => 7,
        VocabPreType::Refact => 8,
        VocabPreType::CommandR => 9,
        VocabPreType::Stablelm2 => 10,
        VocabPreType::Qwen2 => 11,
        VocabPreType::Olmo => 12,
        VocabPreType::Dbrx => 13,
        VocabPreType::Smaug => 14,
        VocabPreType::Poro => 15,
        VocabPreType::ChatGlm3 => 16,
        VocabPreType::ChatGlm4 => 17,
        VocabPreType::Viking => 18,
        VocabPreType::Jais => 19,
        VocabPreType::Tekken => 20,
        VocabPreType::Smollm => 21,
        VocabPreType::Codeshell => 22,
        VocabPreType::Bloom => 23,
        VocabPreType::Gpt3Finnish => 24,
        VocabPreType::Exaone => 25,
        VocabPreType::Chameleon => 26,
        VocabPreType::Minerva => 27,
        VocabPreType::Deepseek3Llm => 28,
        VocabPreType::Gpt4o => 29,
        VocabPreType::Unknown(val) => val,
    }
}

/// RoPE (Rotary Position Embedding) types supported by the model
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum RopeType {
    /// No RoPE
    None,
    /// Normal RoPE
    Norm,
    /// NeoX style RoPE
    Neox,
    /// mRoPE (multi-scale RoPE)
    Mrope,
    /// Vision model RoPE
    Vision,
    /// Unknown RoPE type
    Unknown(i32),
}


/// Converts from C llama_rope_type enum to Rust RopeType enum
///
/// Maps rotary position embedding (RoPE) types used in the model:
/// - None: No RoPE applied
/// - Norm: Standard RoPE implementation
/// - Neox: GPT-NeoX style RoPE implementation
/// - Mrope: mRoPE (multi-scale RoPE) implementation
/// - Vision: Vision model specific RoPE implementation
/// - Unknown: For unrecognized RoPE types
impl From<llama_rope_type> for RopeType {
    fn from(rope_type: llama_rope_type) -> Self {
        match rope_type {
            llama_rope_type_LLAMA_ROPE_TYPE_NONE => RopeType::None,
            llama_rope_type_LLAMA_ROPE_TYPE_NORM => RopeType::Norm,
            llama_rope_type_LLAMA_ROPE_TYPE_NEOX => RopeType::Neox,
            llama_rope_type_LLAMA_ROPE_TYPE_MROPE => RopeType::Mrope,
            llama_rope_type_LLAMA_ROPE_TYPE_VISION => RopeType::Vision,
            unknown => RopeType::Unknown(unknown),
        }
    }
}

/// Converts from Rust RopeType enum to C llama_rope_type enum
///
/// Maps rotary position embedding (RoPE) types back to their C representation:
/// - None -> LLAMA_ROPE_TYPE_NONE: No RoPE applied
/// - Norm -> LLAMA_ROPE_TYPE_NORM: Standard RoPE implementation
/// - Neox -> LLAMA_ROPE_TYPE_NEOX: GPT-NeoX style RoPE implementation
/// - Mrope -> LLAMA_ROPE_TYPE_MROPE: mRoPE (multi-scale RoPE) implementation
/// - Vision -> LLAMA_ROPE_TYPE_VISION: Vision model specific RoPE implementation
/// - Unknown -> Raw value: For unrecognized RoPE types
impl From<RopeType> for llama_rope_type {
    fn from(rope_type: RopeType) -> Self {
        match rope_type {
            RopeType::None => llama_rope_type_LLAMA_ROPE_TYPE_NONE,
            RopeType::Norm => llama_rope_type_LLAMA_ROPE_TYPE_NORM,
            RopeType::Neox => llama_rope_type_LLAMA_ROPE_TYPE_NEOX,
            RopeType::Mrope => llama_rope_type_LLAMA_ROPE_TYPE_MROPE,
            RopeType::Vision => llama_rope_type_LLAMA_ROPE_TYPE_VISION,
            RopeType::Unknown(val) => val,
        }
    }
}

/// RoPE (Rotary Position Embedding) scaling types supported by the model
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum RopeScalingType {
    /// Unspecified RoPE scaling type (-1)
    Unspecified,
    /// No RoPE scaling (0)
    None,
    /// Linear RoPE scaling (1)
    Linear,
    /// YaRN (Yet another RoPE with Norms) scaling (2)
    Yarn,
    /// LongRoPE scaling (3)
    Longrope,
    /// Unknown RoPE scaling type
    Unknown(i32),
}

/// Converts from C llama_rope_scaling_type enum to Rust RopeScalingType enum
///
/// Maps RoPE (Rotary Position Embedding) scaling types:
/// - Unspecified: Unspecified RoPE scaling type (-1)
/// - None: No RoPE scaling (0)
/// - Linear: Linear RoPE scaling (1)
/// - Yarn: YaRN (Yet another RoPE with Norms) scaling (2)
/// - Longrope: LongRoPE scaling (3)
/// - Unknown: For unrecognized RoPE scaling types
impl From<llama_rope_scaling_type> for RopeScalingType {
    fn from(scaling_type: llama_rope_scaling_type) -> Self {
        match scaling_type {
            llama_rope_scaling_type_LLAMA_ROPE_SCALING_TYPE_UNSPECIFIED => RopeScalingType::Unspecified,
            llama_rope_scaling_type_LLAMA_ROPE_SCALING_TYPE_NONE => RopeScalingType::None,
            llama_rope_scaling_type_LLAMA_ROPE_SCALING_TYPE_LINEAR => RopeScalingType::Linear,
            llama_rope_scaling_type_LLAMA_ROPE_SCALING_TYPE_YARN => RopeScalingType::Yarn,
            llama_rope_scaling_type_LLAMA_ROPE_SCALING_TYPE_LONGROPE => RopeScalingType::Longrope,
            unknown => RopeScalingType::Unknown(unknown),
        }
    }
}

/// Converts from Rust RopeScalingType enum to C llama_rope_scaling_type enum
///
/// Maps RoPE (Rotary Position Embedding) scaling types back to their C representation:
/// - Unspecified -> LLAMA_ROPE_SCALING_TYPE_UNSPECIFIED: Unspecified RoPE scaling type (-1)
/// - None -> LLAMA_ROPE_SCALING_TYPE_NONE: No RoPE scaling (0)
/// - Linear -> LLAMA_ROPE_SCALING_TYPE_LINEAR: Linear RoPE scaling (1)
/// - Yarn -> LLAMA_ROPE_SCALING_TYPE_YARN: YaRN (Yet another RoPE with Norms) scaling (2)
/// - Longrope -> LLAMA_ROPE_SCALING_TYPE_LONGROPE: LongRoPE scaling (3)
/// - Unknown -> Raw value: For unrecognized RoPE scaling types
impl From<RopeScalingType> for llama_rope_scaling_type {
    fn from(scaling_type: RopeScalingType) -> Self {
        match scaling_type {
            RopeScalingType::Unspecified => llama_rope_scaling_type_LLAMA_ROPE_SCALING_TYPE_UNSPECIFIED,
            RopeScalingType::None => llama_rope_scaling_type_LLAMA_ROPE_SCALING_TYPE_NONE,
            RopeScalingType::Linear => llama_rope_scaling_type_LLAMA_ROPE_SCALING_TYPE_LINEAR,
            RopeScalingType::Yarn => llama_rope_scaling_type_LLAMA_ROPE_SCALING_TYPE_YARN,
            RopeScalingType::Longrope => llama_rope_scaling_type_LLAMA_ROPE_SCALING_TYPE_LONGROPE,
            RopeScalingType::Unknown(val) => val,
        }
    }
}

/// Token types used by the model
/// Note: This is a temporary enum that will be removed once per-token attributes are available from GGUF files
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TokenType {
    /// Undefined token type (0)
    Undefined,
    /// Normal token (1)
    Normal,
    /// Unknown token (2)
    Unknown,
    /// Control token (3)
    Control,
    /// User-defined token (4)
    UserDefined,
    /// Unused token (5)
    Unused,
    /// Byte token (6)
    Byte,
    /// Custom token type not covered by standard types
    Custom(u32),
}


/// Converts from C llama_token_type enum to Rust TokenType enum
///
/// Maps token type classifications:
/// - Undefined -> LLAMA_TOKEN_TYPE_UNDEFINED (0): Default undefined token type
/// - Normal -> LLAMA_TOKEN_TYPE_NORMAL (1): Standard vocabulary token
/// - Unknown -> LLAMA_TOKEN_TYPE_UNKNOWN (2): Unknown/special token
/// - Control -> LLAMA_TOKEN_TYPE_CONTROL (3): Control token (e.g. BOS, EOS)
/// - UserDefined -> LLAMA_TOKEN_TYPE_USER_DEFINED (4): Custom user-defined token
/// - Unused -> LLAMA_TOKEN_TYPE_UNUSED (5): Token reserved but not used
/// - Byte -> LLAMA_TOKEN_TYPE_BYTE (6): Raw byte token
/// - Custom -> Raw value: For non-standard token types
impl From<llama_token_type> for TokenType {
    fn from(token_type: llama_token_type) -> Self {
        match token_type {
            llama_token_type_LLAMA_TOKEN_TYPE_UNDEFINED => TokenType::Undefined,
            llama_token_type_LLAMA_TOKEN_TYPE_NORMAL => TokenType::Normal,
            llama_token_type_LLAMA_TOKEN_TYPE_UNKNOWN => TokenType::Unknown,
            llama_token_type_LLAMA_TOKEN_TYPE_CONTROL => TokenType::Control,
            llama_token_type_LLAMA_TOKEN_TYPE_USER_DEFINED => TokenType::UserDefined,
            llama_token_type_LLAMA_TOKEN_TYPE_UNUSED => TokenType::Unused,
            llama_token_type_LLAMA_TOKEN_TYPE_BYTE => TokenType::Byte,
            unknown => TokenType::Custom(unknown),
        }
    }
}

/// Converts from Rust TokenType enum to C llama_token_type enum
///
/// Maps token type classifications back to their C representation:
/// - Undefined -> LLAMA_TOKEN_TYPE_UNDEFINED (0): Default undefined token type
/// - Normal -> LLAMA_TOKEN_TYPE_NORMAL (1): Standard vocabulary token
/// - Unknown -> LLAMA_TOKEN_TYPE_UNKNOWN (2): Unknown/special token
/// - Control -> LLAMA_TOKEN_TYPE_CONTROL (3): Control token (e.g. BOS, EOS)
/// - UserDefined -> LLAMA_TOKEN_TYPE_USER_DEFINED (4): Custom user-defined token
/// - Unused -> LLAMA_TOKEN_TYPE_UNUSED (5): Token reserved but not used
/// - Byte -> LLAMA_TOKEN_TYPE_BYTE (6): Raw byte token
/// - Custom -> Raw value: For non-standard token types
impl From<TokenType> for llama_token_type {
    fn from(token_type: TokenType) -> Self {
        match token_type {
            TokenType::Undefined => llama_token_type_LLAMA_TOKEN_TYPE_UNDEFINED,
            TokenType::Normal => llama_token_type_LLAMA_TOKEN_TYPE_NORMAL,
            TokenType::Unknown => llama_token_type_LLAMA_TOKEN_TYPE_UNKNOWN,
            TokenType::Control => llama_token_type_LLAMA_TOKEN_TYPE_CONTROL,
            TokenType::UserDefined => llama_token_type_LLAMA_TOKEN_TYPE_USER_DEFINED,
            TokenType::Unused => llama_token_type_LLAMA_TOKEN_TYPE_UNUSED,
            TokenType::Byte => llama_token_type_LLAMA_TOKEN_TYPE_BYTE,
            TokenType::Custom(val) => val,
        }
    }
}

bitflags::bitflags! {
    /// Token attributes used by the model
    /// These are bit flags that can be combined to represent multiple attributes
    #[derive(Debug, Clone, Copy, PartialEq, Eq)]
    pub struct TokenAttr: u32 {
        /// Undefined token attribute (0)
        const UNDEFINED = llama_token_attr_LLAMA_TOKEN_ATTR_UNDEFINED;
        /// Unknown token (1 << 0)
        const UNKNOWN = llama_token_attr_LLAMA_TOKEN_ATTR_UNKNOWN;
        /// Unused token (1 << 1)
        const UNUSED = llama_token_attr_LLAMA_TOKEN_ATTR_UNUSED;
        /// Normal token (1 << 2)
        const NORMAL = llama_token_attr_LLAMA_TOKEN_ATTR_NORMAL;
        /// Control/special token (1 << 3)
        const CONTROL = llama_token_attr_LLAMA_TOKEN_ATTR_CONTROL;
        /// User-defined token (1 << 4)
        const USER_DEFINED = llama_token_attr_LLAMA_TOKEN_ATTR_USER_DEFINED;
        /// Byte token (1 << 5)
        const BYTE = llama_token_attr_LLAMA_TOKEN_ATTR_BYTE;
        /// Normalized token (1 << 6)
        const NORMALIZED = llama_token_attr_LLAMA_TOKEN_ATTR_NORMALIZED;
        /// Left-stripped token (1 << 7)
        const LSTRIP = llama_token_attr_LLAMA_TOKEN_ATTR_LSTRIP;
        /// Right-stripped token (1 << 8)
        const RSTRIP = llama_token_attr_LLAMA_TOKEN_ATTR_RSTRIP;
        /// Single word token (1 << 9)
        const SINGLE_WORD = llama_token_attr_LLAMA_TOKEN_ATTR_SINGLE_WORD;
    }
}

/// Converts from C llama_token_attr bitflags to Rust TokenAttr bitflags
///
/// Maps token attribute flags:
/// - LLAMA_TOKEN_ATTR_UNDEFINED (0): Default undefined token attribute
/// - LLAMA_TOKEN_ATTR_UNKNOWN (1<<0): Unknown token attribute
/// - LLAMA_TOKEN_ATTR_UNUSED (1<<1): Unused token attribute
/// - LLAMA_TOKEN_ATTR_NORMAL (1<<2): Normal token attribute
/// - LLAMA_TOKEN_ATTR_CONTROL (1<<3): Control token attribute
/// - LLAMA_TOKEN_ATTR_USER_DEFINED (1<<4): User-defined token attribute
/// - LLAMA_TOKEN_ATTR_BYTE (1<<5): Byte token attribute
/// - LLAMA_TOKEN_ATTR_NORMALIZED (1<<6): Normalized token attribute
/// - LLAMA_TOKEN_ATTR_LSTRIP (1<<7): Left-stripped token attribute
/// - LLAMA_TOKEN_ATTR_RSTRIP (1<<8): Right-stripped token attribute
/// - LLAMA_TOKEN_ATTR_SINGLE_WORD (1<<9): Single word token attribute
///
/// Uses from_bits_truncate to safely handle any undefined bits in the input value
impl From<llama_token_attr> for TokenAttr {
    fn from(attr: llama_token_attr) -> Self {
        TokenAttr::from_bits_truncate(attr)
    }
}


/// Converts from Rust TokenAttr bitflags to C llama_token_attr bits
///
/// Maps token attributes to their raw bit representation:
/// - Converts the Rust bitflags structure into the raw C integer type
/// - Preserves all attribute flags like BOS, EOS, etc. during conversion
/// - Used when passing token attributes to llama.cpp functions
impl From<TokenAttr> for llama_token_attr {
    fn from(attr: TokenAttr) -> Self {
        attr.bits()
    }
}

/// Model file types and quantization formats
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum FileType {
    /// Full precision 32-bit float
    AllF32,
    /// Mixed precision 16-bit float (except 1d tensors)
    MostlyF16,
    /// 4-bit integer quantization, version 0 (except 1d tensors)
    MostlyQ4_0,
    /// 4-bit integer quantization, version 1 (except 1d tensors)
    MostlyQ4_1,
    /// 8-bit integer quantization (except 1d tensors)
    MostlyQ8_0,
    /// 5-bit integer quantization, version 0 (except 1d tensors)
    MostlyQ5_0,
    /// 5-bit integer quantization, version 1 (except 1d tensors)
    MostlyQ5_1,
    /// 2-bit K-quantization (except 1d tensors)
    MostlyQ2K,
    /// 3-bit K-quantization, small model (except 1d tensors)
    MostlyQ3KS,
    /// 3-bit K-quantization, medium model (except 1d tensors)
    MostlyQ3KM,
    /// 3-bit K-quantization, large model (except 1d tensors)
    MostlyQ3KL,
    /// 4-bit K-quantization, small model (except 1d tensors)
    MostlyQ4KS,
    /// 4-bit K-quantization, medium model (except 1d tensors)
    MostlyQ4KM,
    /// 5-bit K-quantization, small model (except 1d tensors)
    MostlyQ5KS,
    /// 5-bit K-quantization, medium model (except 1d tensors)
    MostlyQ5KM,
    /// 6-bit K-quantization (except 1d tensors)
    MostlyQ6K,
    /// 2-bit integer quantization, extra extra small model (except 1d tensors)
    MostlyIQ2XXS,
    /// 2-bit integer quantization, extra small model (except 1d tensors)
    MostlyIQ2XS,
    /// 2-bit K-quantization, small model (except 1d tensors)
    MostlyQ2KS,
    /// 3-bit integer quantization, extra small model (except 1d tensors)
    MostlyIQ3XS,
    /// 3-bit integer quantization, extra extra small model (except 1d tensors)
    MostlyIQ3XXS,
    /// 1-bit integer quantization, small model (except 1d tensors)
    MostlyIQ1S,
    /// 4-bit non-linear quantization (except 1d tensors)
    MostlyIQ4NL,
    /// 3-bit integer quantization, small model (except 1d tensors)
    MostlyIQ3S,
    /// 3-bit integer quantization, medium model (except 1d tensors)
    MostlyIQ3M,
    /// 2-bit integer quantization, small model (except 1d tensors)
    MostlyIQ2S,
    /// 2-bit integer quantization, medium model (except 1d tensors)
    MostlyIQ2M,
    /// 4-bit integer quantization, extra small model (except 1d tensors)
    MostlyIQ4XS,
    /// 1-bit integer quantization, medium model (except 1d tensors)
    MostlyIQ1M,
    /// Brain floating point 16-bit (except 1d tensors)
    MostlyBF16,
    /// 1-bit tensor quantization, version 0 (except 1d tensors)
    MostlyTQ1_0,
    /// 2-bit tensor quantization, version 0 (except 1d tensors)
    MostlyTQ2_0,
    /// File type was not specified in the model file
    Guessed,
    /// Unknown file type
    Unknown(u32),
}


/// Converts from C llama_ftype enum to Rust FileType enum
///
/// Maps model file quantization types:
/// - AllF32: All tensors in F32 format
/// - MostlyF16: Most tensors in F16 format
/// - MostlyQ4_0: Most tensors in Q4_0 format
/// - MostlyQ4_1: Most tensors in Q4_1 format
/// - MostlyQ8_0: Most tensors in Q8_0 format
/// - MostlyQ5_0: Most tensors in Q5_0 format
/// - MostlyQ5_1: Most tensors in Q5_1 format
/// - MostlyQ2K: Most tensors in Q2_K format
/// - MostlyQ3KS: Most tensors in Q3_K small format
/// - MostlyQ3KM: Most tensors in Q3_K medium format
/// - MostlyQ3KL: Most tensors in Q3_K large format
/// - MostlyQ4KS: Most tensors in Q4_K small format
/// - MostlyQ4KM: Most tensors in Q4_K medium format
/// - MostlyQ5KS: Most tensors in Q5_K small format
/// - MostlyQ5KM: Most tensors in Q5_K medium format
/// - MostlyQ6K: Most tensors in Q6_K format
/// - MostlyIQ2XXS: Most tensors in IQ2 extra-extra-small format
/// - MostlyIQ2XS: Most tensors in IQ2 extra-small format
/// - MostlyQ2KS: Most tensors in Q2_K small format
/// - MostlyIQ3XS: Most tensors in IQ3 extra-small format
/// - MostlyIQ3XXS: Most tensors in IQ3 extra-extra-small format
/// - MostlyIQ1S: Most tensors in IQ1 small format
/// - MostlyIQ4NL: Most tensors in IQ4 non-linear format
/// - MostlyIQ3S: Most tensors in IQ3 small format
/// - MostlyIQ3M: Most tensors in IQ3 medium format
/// - MostlyIQ2S: Most tensors in IQ2 small format
/// - MostlyIQ2M: Most tensors in IQ2 medium format
/// - MostlyIQ4XS: Most tensors in IQ4 extra-small format
/// - MostlyIQ1M: Most tensors in IQ1 medium format
/// - MostlyBF16: Most tensors in BF16 format
/// - MostlyTQ1_0: Most tensors in TQ1_0 format
/// - MostlyTQ2_0: Most tensors in TQ2_0 format
/// - Guessed: File type was guessed from the file content
/// - Unknown: For unrecognized quantization types
impl From<llama_ftype> for FileType {
    fn from(ftype: llama_ftype) -> Self {
        match ftype {
            llama_ftype_LLAMA_FTYPE_ALL_F32 => FileType::AllF32,
            llama_ftype_LLAMA_FTYPE_MOSTLY_F16 => FileType::MostlyF16,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q4_0 => FileType::MostlyQ4_0,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q4_1 => FileType::MostlyQ4_1,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q8_0 => FileType::MostlyQ8_0,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q5_0 => FileType::MostlyQ5_0,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q5_1 => FileType::MostlyQ5_1,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q2_K => FileType::MostlyQ2K,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q3_K_S => FileType::MostlyQ3KS,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q3_K_M => FileType::MostlyQ3KM,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q3_K_L => FileType::MostlyQ3KL,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q4_K_S => FileType::MostlyQ4KS,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q4_K_M => FileType::MostlyQ4KM,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q5_K_S => FileType::MostlyQ5KS,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q5_K_M => FileType::MostlyQ5KM,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q6_K => FileType::MostlyQ6K,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ2_XXS => FileType::MostlyIQ2XXS,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ2_XS => FileType::MostlyIQ2XS,
            llama_ftype_LLAMA_FTYPE_MOSTLY_Q2_K_S => FileType::MostlyQ2KS,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ3_XS => FileType::MostlyIQ3XS,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ3_XXS => FileType::MostlyIQ3XXS,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ1_S => FileType::MostlyIQ1S,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ4_NL => FileType::MostlyIQ4NL,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ3_S => FileType::MostlyIQ3S,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ3_M => FileType::MostlyIQ3M,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ2_S => FileType::MostlyIQ2S,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ2_M => FileType::MostlyIQ2M,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ4_XS => FileType::MostlyIQ4XS,
            llama_ftype_LLAMA_FTYPE_MOSTLY_IQ1_M => FileType::MostlyIQ1M,
            llama_ftype_LLAMA_FTYPE_MOSTLY_BF16 => FileType::MostlyBF16,
            llama_ftype_LLAMA_FTYPE_MOSTLY_TQ1_0 => FileType::MostlyTQ1_0,
            llama_ftype_LLAMA_FTYPE_MOSTLY_TQ2_0 => FileType::MostlyTQ2_0,
            llama_ftype_LLAMA_FTYPE_GUESSED => FileType::Guessed,
            unknown => FileType::Unknown(unknown),
        }
    }
}

/// Converts from Rust FileType enum to C llama_ftype enum
///
/// Maps model file quantization types back to their C representation:
/// - AllF32 -> LLAMA_FTYPE_ALL_F32: All tensors in F32
/// - MostlyF16 -> LLAMA_FTYPE_MOSTLY_F16: Most tensors in F16
/// - MostlyQ4_0 -> LLAMA_FTYPE_MOSTLY_Q4_0: Most tensors in Q4_0
/// - MostlyQ4_1 -> LLAMA_FTYPE_MOSTLY_Q4_1: Most tensors in Q4_1
/// - MostlyQ8_0 -> LLAMA_FTYPE_MOSTLY_Q8_0: Most tensors in Q8_0
/// - MostlyQ5_0 -> LLAMA_FTYPE_MOSTLY_Q5_0: Most tensors in Q5_0
/// - MostlyQ5_1 -> LLAMA_FTYPE_MOSTLY_Q5_1: Most tensors in Q5_1
/// - MostlyQ2K -> LLAMA_FTYPE_MOSTLY_Q2_K: Most tensors in Q2_K
/// - MostlyQ3KS -> LLAMA_FTYPE_MOSTLY_Q3_K_S: Most tensors in Q3_K small
/// - MostlyQ3KM -> LLAMA_FTYPE_MOSTLY_Q3_K_M: Most tensors in Q3_K medium
/// - MostlyQ3KL -> LLAMA_FTYPE_MOSTLY_Q3_K_L: Most tensors in Q3_K large
/// - MostlyQ4KS -> LLAMA_FTYPE_MOSTLY_Q4_K_S: Most tensors in Q4_K small
/// - MostlyQ4KM -> LLAMA_FTYPE_MOSTLY_Q4_K_M: Most tensors in Q4_K medium
/// - MostlyQ5KS -> LLAMA_FTYPE_MOSTLY_Q5_K_S: Most tensors in Q5_K small
/// - MostlyQ5KM -> LLAMA_FTYPE_MOSTLY_Q5_K_M: Most tensors in Q5_K medium
/// - MostlyQ6K -> LLAMA_FTYPE_MOSTLY_Q6_K: Most tensors in Q6_K
/// - MostlyIQ2XXS -> LLAMA_FTYPE_MOSTLY_IQ2_XXS: Most tensors in IQ2 extra-extra-small
/// - MostlyIQ2XS -> LLAMA_FTYPE_MOSTLY_IQ2_XS: Most tensors in IQ2 extra-small
/// - MostlyQ2KS -> LLAMA_FTYPE_MOSTLY_Q2_K_S: Most tensors in Q2_K small
/// - MostlyIQ3XS -> LLAMA_FTYPE_MOSTLY_IQ3_XS: Most tensors in IQ3 extra-small
/// - MostlyIQ3XXS -> LLAMA_FTYPE_MOSTLY_IQ3_XXS: Most tensors in IQ3 extra-extra-small
/// - MostlyIQ1S -> LLAMA_FTYPE_MOSTLY_IQ1_S: Most tensors in IQ1 small
/// - MostlyIQ4NL -> LLAMA_FTYPE_MOSTLY_IQ4_NL: Most tensors in IQ4 non-linear
/// - MostlyIQ3S -> LLAMA_FTYPE_MOSTLY_IQ3_S: Most tensors in IQ3 small
/// - MostlyIQ3M -> LLAMA_FTYPE_MOSTLY_IQ3_M: Most tensors in IQ3 medium
/// - MostlyIQ2S -> LLAMA_FTYPE_MOSTLY_IQ2_S: Most tensors in IQ2 small
/// - MostlyIQ2M -> LLAMA_FTYPE_MOSTLY_IQ2_M: Most tensors in IQ2 medium
/// - MostlyIQ4XS -> LLAMA_FTYPE_MOSTLY_IQ4_XS: Most tensors in IQ4 extra-small
/// - MostlyIQ1M -> LLAMA_FTYPE_MOSTLY_IQ1_M: Most tensors in IQ1 medium
/// - MostlyBF16 -> LLAMA_FTYPE_MOSTLY_BF16: Most tensors in BF16
/// - MostlyTQ1_0 -> LLAMA_FTYPE_MOSTLY_TQ1_0: Most tensors in TQ1_0
/// - MostlyTQ2_0 -> LLAMA_FTYPE_MOSTLY_TQ2_0: Most tensors in TQ2_0
/// - Guessed -> LLAMA_FTYPE_GUESSED: File type guessed from content
/// - Unknown -> Raw value: For unrecognized quantization types
impl From<FileType> for llama_ftype {
    fn from(ftype: FileType) -> Self {
        match ftype {
            FileType::AllF32 => llama_ftype_LLAMA_FTYPE_ALL_F32,
            FileType::MostlyF16 => llama_ftype_LLAMA_FTYPE_MOSTLY_F16,
            FileType::MostlyQ4_0 => llama_ftype_LLAMA_FTYPE_MOSTLY_Q4_0,
            FileType::MostlyQ4_1 => llama_ftype_LLAMA_FTYPE_MOSTLY_Q4_1,
            FileType::MostlyQ8_0 => llama_ftype_LLAMA_FTYPE_MOSTLY_Q8_0,
            FileType::MostlyQ5_0 => llama_ftype_LLAMA_FTYPE_MOSTLY_Q5_0,
            FileType::MostlyQ5_1 => llama_ftype_LLAMA_FTYPE_MOSTLY_Q5_1,
            FileType::MostlyQ2K => llama_ftype_LLAMA_FTYPE_MOSTLY_Q2_K,
            FileType::MostlyQ3KS => llama_ftype_LLAMA_FTYPE_MOSTLY_Q3_K_S,
            FileType::MostlyQ3KM => llama_ftype_LLAMA_FTYPE_MOSTLY_Q3_K_M,
            FileType::MostlyQ3KL => llama_ftype_LLAMA_FTYPE_MOSTLY_Q3_K_L,
            FileType::MostlyQ4KS => llama_ftype_LLAMA_FTYPE_MOSTLY_Q4_K_S,
            FileType::MostlyQ4KM => llama_ftype_LLAMA_FTYPE_MOSTLY_Q4_K_M,
            FileType::MostlyQ5KS => llama_ftype_LLAMA_FTYPE_MOSTLY_Q5_K_S,
            FileType::MostlyQ5KM => llama_ftype_LLAMA_FTYPE_MOSTLY_Q5_K_M,
            FileType::MostlyQ6K => llama_ftype_LLAMA_FTYPE_MOSTLY_Q6_K,
            FileType::MostlyIQ2XXS => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ2_XXS,
            FileType::MostlyIQ2XS => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ2_XS,
            FileType::MostlyQ2KS => llama_ftype_LLAMA_FTYPE_MOSTLY_Q2_K_S,
            FileType::MostlyIQ3XS => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ3_XS,
            FileType::MostlyIQ3XXS => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ3_XXS,
            FileType::MostlyIQ1S => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ1_S,
            FileType::MostlyIQ4NL => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ4_NL,
            FileType::MostlyIQ3S => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ3_S,
            FileType::MostlyIQ3M => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ3_M,
            FileType::MostlyIQ2S => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ2_S,
            FileType::MostlyIQ2M => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ2_M,
            FileType::MostlyIQ4XS => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ4_XS,
            FileType::MostlyIQ1M => llama_ftype_LLAMA_FTYPE_MOSTLY_IQ1_M,
            FileType::MostlyBF16 => llama_ftype_LLAMA_FTYPE_MOSTLY_BF16,
            FileType::MostlyTQ1_0 => llama_ftype_LLAMA_FTYPE_MOSTLY_TQ1_0,
            FileType::MostlyTQ2_0 => llama_ftype_LLAMA_FTYPE_MOSTLY_TQ2_0,
            FileType::Guessed => llama_ftype_LLAMA_FTYPE_GUESSED,
            FileType::Unknown(val) => val,
        }
    }
}

/// Pooling types for embeddings
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PoolingType {
    /// Unspecified pooling type (-1)
    Unspecified,
    /// No pooling (0)
    None,
    /// Mean pooling - average all token embeddings (1)
    Mean,
    /// CLS pooling - use first token's embedding (2)
    Cls,
    /// Last pooling - use last token's embedding (3)
    Last,
    /// Rank pooling - used by reranking models to attach classification head to graph (4)
    Rank,
    /// Unknown pooling type
    Unknown(i32),
}

/// Converts from C llama_pooling_type enum to Rust PoolingType enum
///
/// Maps pooling types used for sequence embeddings:
/// - Unspecified: Unspecified pooling type (-1)
/// - None: No pooling applied (0)
/// - Mean: Average pooling over sequence (1)
/// - Cls: Use [CLS] token embedding (2)
/// - Last: Use last token embedding (3)
/// - Rank: Rank pooling over sequence (4)
/// - Unknown: For unrecognized pooling types
impl From<llama_pooling_type> for PoolingType {
    fn from(pooling_type: llama_pooling_type) -> Self {
        match pooling_type {
            llama_pooling_type_LLAMA_POOLING_TYPE_UNSPECIFIED => PoolingType::Unspecified,
            llama_pooling_type_LLAMA_POOLING_TYPE_NONE => PoolingType::None,
            llama_pooling_type_LLAMA_POOLING_TYPE_MEAN => PoolingType::Mean,
            llama_pooling_type_LLAMA_POOLING_TYPE_CLS => PoolingType::Cls,
            llama_pooling_type_LLAMA_POOLING_TYPE_LAST => PoolingType::Last,
            llama_pooling_type_LLAMA_POOLING_TYPE_RANK => PoolingType::Rank,
            unknown => PoolingType::Unknown(unknown),
        }
    }
}

/// Converts from Rust PoolingType enum to C llama_pooling_type enum
///
/// Maps pooling types back to their C representation for sequence embeddings:
/// - Unspecified -> LLAMA_POOLING_TYPE_UNSPECIFIED: Unspecified pooling type (-1)
/// - None -> LLAMA_POOLING_TYPE_NONE: No pooling applied (0)
/// - Mean -> LLAMA_POOLING_TYPE_MEAN: Average pooling over sequence (1)
/// - Cls -> LLAMA_POOLING_TYPE_CLS: Use [CLS] token embedding (2)
/// - Last -> LLAMA_POOLING_TYPE_LAST: Use last token embedding (3)
/// - Rank -> LLAMA_POOLING_TYPE_RANK: Rank pooling over sequence (4)
/// - Unknown -> Raw value: For unrecognized pooling types
impl From<PoolingType> for llama_pooling_type {
    fn from(pooling_type: PoolingType) -> Self {
        match pooling_type {
            PoolingType::Unspecified => llama_pooling_type_LLAMA_POOLING_TYPE_UNSPECIFIED,
            PoolingType::None => llama_pooling_type_LLAMA_POOLING_TYPE_NONE,
            PoolingType::Mean => llama_pooling_type_LLAMA_POOLING_TYPE_MEAN,
            PoolingType::Cls => llama_pooling_type_LLAMA_POOLING_TYPE_CLS,
            PoolingType::Last => llama_pooling_type_LLAMA_POOLING_TYPE_LAST,
            PoolingType::Rank => llama_pooling_type_LLAMA_POOLING_TYPE_RANK,
            PoolingType::Unknown(val) => val,
        }
    }
}

/// Attention types supported by the model
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AttentionType {
    /// Unspecified attention type (-1)
    Unspecified,
    /// Causal attention - each token can only attend to previous tokens (0)
    Causal,
    /// Non-causal attention - tokens can attend to all other tokens (1)
    NonCausal,
    /// Unknown attention type
    Unknown(i32),
}


/// Converts from C llama_attention_type enum to Rust AttentionType enum
///
/// Maps attention mechanism types used in the model:
/// - Unspecified: Unspecified attention type (-1)
/// - Causal: Standard causal attention (0), each token can only attend to previous tokens
/// - NonCausal: Bidirectional attention (1), tokens can attend to both previous and future tokens
/// - Unknown: For unrecognized attention types
impl From<llama_attention_type> for AttentionType {
    fn from(attention_type: llama_attention_type) -> Self {
        match attention_type {
            llama_attention_type_LLAMA_ATTENTION_TYPE_UNSPECIFIED => AttentionType::Unspecified,
            llama_attention_type_LLAMA_ATTENTION_TYPE_CAUSAL => AttentionType::Causal,
            llama_attention_type_LLAMA_ATTENTION_TYPE_NON_CAUSAL => AttentionType::NonCausal,
            unknown => AttentionType::Unknown(unknown),
        }
    }
}

/// Converts from Rust AttentionType enum to C llama_attention_type enum
///
/// Maps attention mechanism types back to their C representation:
/// - Unspecified -> LLAMA_ATTENTION_TYPE_UNSPECIFIED: Unspecified attention type (-1)
/// - Causal -> LLAMA_ATTENTION_TYPE_CAUSAL: Standard causal attention (0)
/// - NonCausal -> LLAMA_ATTENTION_TYPE_NON_CAUSAL: Bidirectional attention (1)
/// - Unknown -> Raw value: For unrecognized attention types
impl From<AttentionType> for llama_attention_type {
    fn from(attention_type: AttentionType) -> Self {
        match attention_type {
            AttentionType::Unspecified => llama_attention_type_LLAMA_ATTENTION_TYPE_UNSPECIFIED,
            AttentionType::Causal => llama_attention_type_LLAMA_ATTENTION_TYPE_CAUSAL,
            AttentionType::NonCausal => llama_attention_type_LLAMA_ATTENTION_TYPE_NON_CAUSAL,
            AttentionType::Unknown(val) => val,
        }
    }
}

/// Model split modes for multi-GPU execution
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SplitMode {
    /// Single GPU execution (0)
    None,
    /// Split layers and KV across GPUs (1)
    Layer,
    /// Split layers and KV across GPUs, use tensor parallelism if supported (2)
    Row,
    /// Unknown split mode
    Unknown(u32),
}

/// Converts from C llama_split_mode enum to Rust SplitMode enum
///
/// Maps model parallelism splitting modes:
/// - None: No model splitting (0)
/// - Layer: Split model across layers (1)
/// - Row: Split model across rows/neurons (2)
/// - Unknown: For unrecognized split modes
impl From<llama_split_mode> for SplitMode {
    fn from(split_mode: llama_split_mode) -> Self {
        match split_mode {
            llama_split_mode_LLAMA_SPLIT_MODE_NONE => SplitMode::None,
            llama_split_mode_LLAMA_SPLIT_MODE_LAYER => SplitMode::Layer,
            llama_split_mode_LLAMA_SPLIT_MODE_ROW => SplitMode::Row,
            unknown => SplitMode::Unknown(unknown),
        }
    }
}



/// Converts from Rust SplitMode enum to C llama_split_mode enum
///
/// Maps model parallelism splitting modes back to their C representation:
/// - None -> LLAMA_SPLIT_MODE_NONE: No model splitting (0)
/// - Layer -> LLAMA_SPLIT_MODE_LAYER: Split model across layers (1)
/// - Row -> LLAMA_SPLIT_MODE_ROW: Split model across rows/neurons (2)
/// - Unknown -> Raw value: For unrecognized split modes
impl From<SplitMode> for llama_split_mode {
    fn from(split_mode: SplitMode) -> Self {
        match split_mode {
            SplitMode::None => llama_split_mode_LLAMA_SPLIT_MODE_NONE,
            SplitMode::Layer => llama_split_mode_LLAMA_SPLIT_MODE_LAYER,
            SplitMode::Row => llama_split_mode_LLAMA_SPLIT_MODE_ROW,
            SplitMode::Unknown(val) => val,
        }
    }
}

/// Token data containing token ID, logit, and probability
#[derive(Debug, Clone, Copy)]
pub struct TokenData {
    /// Token ID
    pub id: Token,
    /// Log-odds of the token
    pub logit: f32,
    /// Probability of the token
    pub p: f32,
}

/// Converts from C llama_token_data struct to Rust TokenData struct
///
/// Maps token scoring data between C and Rust representations:
/// - id: Token identifier (vocabulary index)
/// - logit: Raw logit score for the token
/// - p: Probability score for the token (typically after softmax)
impl From<llama_token_data> for TokenData {
    fn from(data: llama_token_data) -> Self {
        Self {
            id: data.id,
            logit: data.logit,
            p: data.p,
        }
    }
}

/// Converts from Rust TokenData struct to C llama_token_data struct
///
/// Maps token scoring data back to C representation:
/// - id: Token identifier (vocabulary index)
/// - logit: Raw logit score for the token
/// - p: Probability score for the token (typically after softmax)
impl From<TokenData> for llama_token_data {
    fn from(data: TokenData) -> Self {
        Self {
            id: data.id,
            logit: data.logit,
            p: data.p,
        }
    }
}

/// Callback function type for progress updates during model operations.
/// Returns true to continue, false to abort.
pub type ProgressCallback = Option<unsafe extern "C" fn(progress: f32, user_data: *mut std::ffi::c_void) -> bool>;

/// A batch of tokens for model input/output.
/// Can contain input about one or many sequences.
#[derive(Debug)]
pub struct Batch {
    /// Number of tokens in the batch
    pub n_tokens: i32,
    /// Token IDs (used when embeddings is None)
    pub token: Option<Vec<Token>>,
    /// Token embeddings (used when token is None)
    pub embd: Option<Vec<f32>>,
    /// Token positions in sequence (None = auto-tracked)
    pub pos: Option<Vec<Pos>>,
    /// Number of sequence IDs per token
    pub n_seq_id: Option<Vec<i32>>,
    /// Sequence IDs for each token (None = seq_id 0)
    pub seq_id: Option<Vec<Vec<SeqId>>>,
    /// Whether to output logits for each token (None = last token only)
    pub logits: Option<Vec<i8>>,
}

impl Batch {
    /// Create a new empty batch
    pub fn new() -> Self {
        Self {
            n_tokens: 0,
            token: None,
            embd: None,
            pos: None,
            n_seq_id: None,
            seq_id: None,
            logits: None,
        }
    }

    /// Initialize a new batch of tokens with proper memory management
    /// If embd is non-zero, allocates embedding buffer instead of token buffer
    /// This creates a Rust-managed batch that doesn't need explicit freeing
    pub fn batch_init(n_tokens: i32, embd: i32, n_seq_max: i32) -> Self {
        // Create a Rust-managed batch instead of using C allocation
        // This avoids memory management issues between C and Rust
        let mut batch = Self::new();
        batch.n_tokens = 0; // Start with 0 tokens, will be set when adding tokens

        if embd > 0 {
            // Allocate embedding buffer
            batch.embd = Some(vec![0.0; (n_tokens * embd) as usize]);
        } else {
            // Allocate token buffer
            batch.token = Some(vec![0; n_tokens as usize]);
        }

        // Allocate other buffers
        batch.pos = Some(vec![0; n_tokens as usize]);
        batch.n_seq_id = Some(vec![0; n_tokens as usize]);
        batch.seq_id = Some(vec![vec![0; n_seq_max as usize]; n_tokens as usize]);
        batch.logits = Some(vec![0; n_tokens as usize]);

        batch
    }

    /// Convert to C representation
    /// WARNING: The returned llama_batch contains pointers to data owned by this Batch.
    /// The Batch must remain alive for the lifetime of the returned llama_batch.
    pub fn into_c_batch(self) -> llama_batch {
        let token_ptr = self.token.as_ref().map_or(std::ptr::null_mut(), |v| v.as_ptr() as *mut Token);
        let embd_ptr = self.embd.as_ref().map_or(std::ptr::null_mut(), |v| v.as_ptr() as *mut f32);
        let pos_ptr = self.pos.as_ref().map_or(std::ptr::null_mut(), |v| v.as_ptr() as *mut Pos);
        let n_seq_id_ptr = self.n_seq_id.as_ref().map_or(std::ptr::null_mut(), |v| v.as_ptr() as *mut i32);

        // Fix: Use null pointer for seq_id if not properly structured
        // The seq_id field requires complex pointer-to-pointer setup that's error-prone
        let seq_id_ptr = std::ptr::null_mut(); // Simplified: let llama.cpp handle default seq_id=0

        let logits_ptr = self.logits.as_ref().map_or(std::ptr::null_mut(), |v| v.as_ptr() as *mut i8);

        llama_batch {
            n_tokens: self.n_tokens,
            token: token_ptr,
            embd: embd_ptr,
            pos: pos_ptr,
            n_seq_id: n_seq_id_ptr,
            seq_id: seq_id_ptr,
            logits: logits_ptr,
        }
    }

    /// Create from C representation by copying data (safer than taking ownership)
    pub unsafe fn from_c_batch(batch: &llama_batch) -> Self {
        let token = if !batch.token.is_null() {
            let slice = std::slice::from_raw_parts(batch.token, batch.n_tokens as usize);
            Some(slice.to_vec())
        } else {
            None
        };

        let embd = if !batch.embd.is_null() {
            let slice = std::slice::from_raw_parts(batch.embd, batch.n_tokens as usize);
            Some(slice.to_vec())
        } else {
            None
        };

        let pos = if !batch.pos.is_null() {
            let slice = std::slice::from_raw_parts(batch.pos, batch.n_tokens as usize);
            Some(slice.to_vec())
        } else {
            None
        };

        let n_seq_id = if !batch.n_seq_id.is_null() {
            let slice = std::slice::from_raw_parts(batch.n_seq_id, batch.n_tokens as usize);
            Some(slice.to_vec())
        } else {
            None
        };

        let seq_id = if !batch.seq_id.is_null() && !batch.n_seq_id.is_null() {
            let mut seq_ids = Vec::with_capacity(batch.n_tokens as usize);
            for i in 0..batch.n_tokens {
                let n_ids = *batch.n_seq_id.add(i as usize);
                let ptr = *batch.seq_id.add(i as usize);
                if !ptr.is_null() {
                    let slice = std::slice::from_raw_parts(ptr, n_ids as usize);
                    seq_ids.push(slice.to_vec());
                } else {
                    seq_ids.push(vec![0; n_ids as usize]);
                }
            }
            Some(seq_ids)
        } else {
            None
        };

        let logits = if !batch.logits.is_null() {
            Some(Vec::from_raw_parts(batch.logits as *mut _, batch.n_tokens as usize, batch.n_tokens as usize))
        } else {
            None
        };

        Self {
            n_tokens: batch.n_tokens,
            token,
            embd,
            pos,
            n_seq_id,
            seq_id,
            logits,
        }
    }
}

/// Implements the Default trait for Batch struct
///
/// Creates a new empty batch with default settings:
/// - Initializes an empty token batch for model inference
/// - Uses Self::new() which sets up default memory allocations
/// - Used when you need a new Batch without specifying parameters
impl Default for Batch {
    fn default() -> Self {
        Self::new()
    }
}

/// Safe wrapper around llama.cpp library
pub struct LlamaModel {
    model: *mut llama_model,
    ctx: *mut llama_context,
}

impl LlamaModel {
    /// Initialize the llama backend
    pub fn init() {
        unsafe {
            llama_backend_init();
        }
    }

    /// Free the llama backend
    pub fn backend_free() {
        unsafe {
            llama_backend_free();
        }
    }

    /// Initialize NUMA optimizations
    pub fn numa_init(numa: ggml_numa_strategy) {
        unsafe {
            llama_numa_init(numa);
        }
    }

    /// Attach threadpools to the context
    pub unsafe fn attach_threadpool(&mut self, threadpool: ggml_threadpool_t, threadpool_batch: Option<ggml_threadpool_t>) {
        llama_attach_threadpool(self.ctx, threadpool, threadpool_batch.unwrap_or(threadpool));
    }

    /// Detach threadpools from the context
    pub fn detach_threadpool(&mut self) {
        unsafe {
            llama_detach_threadpool(self.ctx);
        }
    }

    /// Load a model from a file path with custom parameters
    pub fn load_model_with_params<P: AsRef<std::path::Path>>(path: P, params: ModelParams) -> Result<Self, String> {
        let path_str = path.as_ref().to_str()
            .ok_or("Invalid path")?;

        let path_cstr = CString::new(path_str)
            .map_err(|e| e.to_string())?;

        // Load the model
        let model = unsafe {
            llama_model_load_from_file(path_cstr.as_ptr(), params.into())
        };

        if model.is_null() {
            return Err("Failed to load model".into());
        }

        // Create default context params
        let ctx_params = unsafe { llama_context_default_params() };

        // Create context from model
        let ctx = unsafe {
            llama_init_from_model(model, ctx_params)
        };

        if ctx.is_null() {
            unsafe { llama_model_free(model) };
            return Err("Failed to create context".into());
        }

        Ok(Self { model, ctx })
    }

    /// Load a model from a file path with default parameters
    pub fn load_model<P: AsRef<std::path::Path>>(path: P) -> Result<Self, String> {
        Self::load_model_with_params(path, ModelParams::default())
    }

    /// Load a model from multiple split files
    pub fn load_model_from_splits<P: AsRef<std::path::Path>>(paths: &[P], params: ModelParams) -> Result<Self, String> {
        if paths.is_empty() {
            return Err("No paths provided for split loading".into());
        }

        // Convert paths to CStrings
        let c_paths: Result<Vec<CString>, String> = paths.iter()
            .map(|p| p.as_ref().to_str()
                .ok_or_else(|| String::from("Invalid path"))
                .and_then(|s| CString::new(s).map_err(|e| e.to_string())))
            .collect();
        let c_paths = c_paths?;

        // Create array of pointers
        let mut path_ptrs: Vec<*const c_char> = c_paths.iter()
            .map(|cs| cs.as_ptr())
            .collect();

        // Load the model
        let model = unsafe {
            llama_model_load_from_splits(
                path_ptrs.as_mut_ptr(),
                path_ptrs.len(),
                params.into()
            )
        };

        if model.is_null() {
            return Err("Failed to load model from splits".into());
        }

        // Create default context params
        let ctx_params = unsafe { llama_context_default_params() };

        // Create context from model
        let ctx = unsafe {
            llama_init_from_model(model, ctx_params)
        };

        if ctx.is_null() {
            unsafe { llama_model_free(model) };
            return Err("Failed to create context".into());
        }

        Ok(Self { model, ctx })
    }

    /// Get number of tokens in model vocabulary
    pub fn n_vocab(&self) -> i32 {
        unsafe {
            let vocab = llama_model_get_vocab(self.model);
            llama_vocab_n_tokens(vocab)
        }
    }

    /// Get model context size
    pub fn n_ctx(&self) -> u32 {
        unsafe {
            llama_n_ctx(self.ctx)
        }
    }

    /// Get current time in microseconds
    pub fn time_us() -> i64 {
        unsafe {
            llama_time_us()
        }
    }

    /// Get maximum number of supported devices
    pub fn max_devices() -> usize {
        unsafe {
            llama_max_devices()
        }
    }

    /// Check if memory mapping is supported
    pub fn supports_mmap() -> bool {
        unsafe {
            llama_supports_mmap()
        }
    }

    /// Check if memory locking is supported
    pub fn supports_mlock() -> bool {
        unsafe {
            llama_supports_mlock()
        }
    }

    /// Check if GPU offloading is supported
    pub fn supports_gpu_offload() -> bool {
        unsafe {
            llama_supports_gpu_offload()
        }
    }

    /// Check if RPC is supported
    pub fn supports_rpc() -> bool {
        unsafe {
            llama_supports_rpc()
        }
    }

    /// Get batch size
    pub fn n_batch(&self) -> u32 {
        unsafe {
            llama_n_batch(self.ctx)
        }
    }

    /// Get micro batch size
    pub fn n_ubatch(&self) -> u32 {
        unsafe {
            llama_n_ubatch(self.ctx)
        }
    }

    /// Get maximum sequence length
    pub fn n_seq_max(&self) -> u32 {
        unsafe {
            llama_n_seq_max(self.ctx)
        }
    }

    /// Get the underlying model pointer
    pub fn get_model(&self) -> *const llama_model {
        self.model
    }

    /// Get the pooling type used by the model
    pub fn pooling_type(&self) -> PoolingType {
        unsafe {
            llama_pooling_type(self.ctx).into()
        }
    }

    /// Get the model's vocabulary
    pub fn model_get_vocab(&self) -> *const llama_vocab {
        unsafe {
            llama_model_get_vocab(self.model)
        }
    }

    /// Get the RoPE type used by the model
    pub fn model_rope_type(&self) -> RopeType {
        unsafe {
            llama_model_rope_type(self.model).into()
        }
    }

    /// Get the context size used during model training
    pub fn model_n_ctx_train(&self) -> i32 {
        unsafe {
            llama_model_n_ctx_train(self.model)
        }
    }

    /// Get the model's embedding dimension
    pub fn model_n_embd(&self) -> i32 {
        unsafe {
            llama_model_n_embd(self.model)
        }
    }

    /// Get the number of layers in the model
    pub fn model_n_layer(&self) -> i32 {
        unsafe {
            llama_model_n_layer(self.model)
        }
    }

    /// Get the number of attention heads in the model
    pub fn model_n_head(&self) -> i32 {
        unsafe {
            llama_model_n_head(self.model)
        }
    }

    /// Get the number of key/value attention heads in the model
    pub fn model_n_head_kv(&self) -> i32 {
        unsafe {
            llama_model_n_head_kv(self.model)
        }
    }

    /// Get the model's RoPE frequency scaling factor
    pub fn model_rope_freq_scale_train(&self) -> f32 {
        unsafe {
            llama_model_rope_freq_scale_train(self.model)
        }
    }

    /// Get metadata value as a string by key name
    /// Returns the length of the string on success, or -1 on failure
    pub fn model_meta_val_str(&self, key: &str, buf_size: usize) -> Result<String, i32> {
        let key = CString::new(key).map_err(|_| -1)?;
        let mut buf = vec![0i8; buf_size];
        let result = unsafe {
            llama_model_meta_val_str(self.model, key.as_ptr(), buf.as_mut_ptr(), buf_size)
        };
        if result < 0 {
            return Err(result);
        }
        let c_str = unsafe { CStr::from_ptr(buf.as_ptr()) };
        Ok(c_str.to_string_lossy().into_owned())
    }

    /// Get the number of metadata key/value pairs
    pub fn model_meta_count(&self) -> i32 {
        unsafe {
            llama_model_meta_count(self.model)
        }
    }

    /// Get metadata key name by index
    /// Returns the length of the string on success, or -1 on failure
    pub fn model_meta_key_by_index(&self, index: i32, buf_size: usize) -> Result<String, i32> {
        let mut buf = vec![0i8; buf_size];
        let result = unsafe {
            llama_model_meta_key_by_index(self.model, index, buf.as_mut_ptr(), buf_size)
        };
        if result < 0 {
            return Err(result);
        }
        let c_str = unsafe { CStr::from_ptr(buf.as_ptr()) };
        Ok(c_str.to_string_lossy().into_owned())
    }

    /// Get metadata value as a string by index
    /// Returns the length of the string on success, or -1 on failure
    pub fn model_meta_val_str_by_index(&self, index: i32, buf_size: usize) -> Result<String, i32> {
        let mut buf = vec![0i8; buf_size];
        let result = unsafe {
            llama_model_meta_val_str_by_index(self.model, index, buf.as_mut_ptr(), buf_size)
        };
        if result < 0 {
            return Err(result);
        }
        let c_str = unsafe { CStr::from_ptr(buf.as_ptr()) };
        Ok(c_str.to_string_lossy().into_owned())
    }

    /// Get a string describing the model type
    /// Returns the length of the string on success, or -1 on failure
    pub fn model_desc(&self, buf_size: usize) -> Result<String, i32> {
        let mut buf = vec![0i8; buf_size];
        let result = unsafe {
            llama_model_desc(self.model, buf.as_mut_ptr(), buf_size)
        };
        if result < 0 {
            return Err(result);
        }
        let c_str = unsafe { CStr::from_ptr(buf.as_ptr()) };
        Ok(c_str.to_string_lossy().into_owned())
    }

    // This appears to be a corrupted function, removing it

    /// Get the total size of all the tensors in the model in bytes
    pub fn model_size(&self) -> u64 {
        unsafe {
            llama_model_size(self.model)
        }
    }

    /// Get the default chat template
    /// Returns None if not available
    pub fn model_chat_template(&self, name: Option<&str>) -> Option<String> {
        let name_cstr = name.map(|n| CString::new(n).ok()).flatten();
        let name_ptr = name_cstr.as_ref().map_or(std::ptr::null(), |cs| cs.as_ptr());
        let result = unsafe {
            llama_model_chat_template(self.model, name_ptr)
        };
        if result.is_null() {
            None
        } else {
            let c_str = unsafe { CStr::from_ptr(result) };
            Some(c_str.to_string_lossy().into_owned())
        }
    }

    /// Get the total number of parameters in the model
    pub fn model_n_params(&self) -> u64 {
        unsafe {
            llama_model_n_params(self.model)
        }
    }

    /// Check if the model contains an encoder that requires llama_encode() call
    pub fn model_has_encoder(&self) -> bool {
        unsafe {
            llama_model_has_encoder(self.model)
        }
    }

    /// Check if the model contains a decoder that requires llama_decode() call
    pub fn model_has_decoder(&self) -> bool {
        unsafe {
            llama_model_has_decoder(self.model)
        }
    }

    /// Get the decoder start token for encoder-decoder models
    /// Returns -1 for other models
    pub fn model_decoder_start_token(&self) -> Token {
        unsafe {
            llama_model_decoder_start_token(self.model)
        }
    }

    /// Check if the model is recurrent (like Mamba, RWKV, etc.)
    pub fn model_is_recurrent(&self) -> bool {
        unsafe {
            llama_model_is_recurrent(self.model)
        }
    }

    /// Quantize a model from one file to another using the specified parameters
    /// Returns Ok(()) on success, Err(String) on failure
    pub fn quantize_model<P: AsRef<std::path::Path>>(
        input_path: P,
        output_path: P,
        params: ModelQuantizeParams
    ) -> Result<(), String> {
        let input_str = input_path.as_ref().to_str()
            .ok_or("Invalid input path")?;
        let output_str = output_path.as_ref().to_str()
            .ok_or("Invalid output path")?;

        let input_cstr = CString::new(input_str)
            .map_err(|e| e.to_string())?;
        let output_cstr = CString::new(output_str)
            .map_err(|e| e.to_string())?;

        let result = unsafe {
            llama_model_quantize(
                input_cstr.as_ptr(),
                output_cstr.as_ptr(),
                &params.into_c_params()
            )
        };

        if result == 0 {
            Ok(())
        } else {
            Err("Model quantization failed".into())
        }
    }

    /// Remove all LoRA adapters from the context
    pub fn clear_adapter_lora(&mut self) {
        unsafe {
            llama_clear_adapter_lora(self.ctx);
        }
    }

    /// Apply a control vector to the context
    /// If data is None, clears the currently loaded vector
    /// n_embd should be the size of a single layer's control
    /// data should point to an n_embd x n_layers buffer starting from layer 1
    /// il_start and il_end are the layer range the vector should apply to (both inclusive)
    pub fn apply_adapter_cvec(
        &mut self,
        data: Option<&[f32]>,
        n_embd: i32,
        il_start: i32,
        il_end: i32
    ) -> bool {
        let (data_ptr, len) = data.map_or((std::ptr::null(), 0), |d| (d.as_ptr(), d.len()));

        unsafe {
            llama_apply_adapter_cvec(
                self.ctx,
                data_ptr,
                len,
                n_embd,
                il_start,
                il_end
            ) == 0
        }
    }

    /// Get the number of tokens in the KV cache
    pub fn get_kv_cache_token_count(&self) -> i32 {
        unsafe {
            llama_kv_self_n_tokens(self.ctx)
        }
    }

    /// Get the number of used cells in the KV cache
    pub fn get_kv_cache_used_cells(&self) -> i32 {
        unsafe {
            llama_kv_self_used_cells(self.ctx)
        }
    }

    /// Clear the KV cache
    pub fn kv_cache_clear(&mut self) {
        unsafe {
            llama_kv_self_clear(self.ctx);
        }
    }

    /// Remove sequence from the KV cache
    pub fn kv_cache_seq_rm(&mut self, seq_id: SeqId, p0: Pos, p1: Pos) -> bool {
        unsafe {
            llama_kv_self_seq_rm(self.ctx, seq_id, p0, p1)
        }
    }

    /// Copy sequence in the KV cache
    pub fn kv_cache_seq_cp(&mut self, seq_id_src: SeqId, seq_id_dst: SeqId, p0: Pos, p1: Pos) {
        unsafe {
            llama_kv_self_seq_cp(self.ctx, seq_id_src, seq_id_dst, p0, p1);
        }
    }

    /// Keep only the specified sequence in the KV cache
    pub fn kv_cache_seq_keep(&mut self, seq_id: SeqId) {
        unsafe {
            llama_kv_self_seq_keep(self.ctx, seq_id);
        }
    }

    /// Add sequence to the KV cache
    pub fn kv_cache_seq_add(&mut self, seq_id: SeqId, p0: Pos, p1: Pos, delta: Pos) {
        unsafe {
            llama_kv_self_seq_add(self.ctx, seq_id, p0, p1, delta);
        }
    }

    /// Divide sequence in the KV cache
    pub fn kv_cache_seq_div(&mut self, seq_id: SeqId, p0: Pos, p1: Pos, d: i32) {
        unsafe {
            llama_kv_self_seq_div(self.ctx, seq_id, p0, p1, d);
        }
    }

    /// Get maximum position in the KV cache for the specified sequence
    pub fn kv_cache_seq_pos_max(&mut self, seq_id: SeqId) -> Pos {
        unsafe {
            llama_kv_self_seq_pos_max(self.ctx, seq_id)
        }
    }

    /// Defragment the KV cache
    pub fn kv_cache_defrag(&mut self) {
        unsafe {
            llama_kv_self_defrag(self.ctx);
        }
    }

    /// Update the KV cache
    pub fn kv_cache_update(&mut self) {
        unsafe {
            llama_kv_self_update(self.ctx);
        }
    }

    /// Check if the KV cache can be shifted
    pub fn kv_cache_can_shift(&self) -> bool {
        unsafe {
            llama_kv_self_can_shift(self.ctx)
        }
    }

    /// Get the size in bytes needed to store the state
    /// Only use when saving the state, not when restoring it
    pub fn state_get_size(&self) -> usize {
        unsafe {
            llama_state_get_size(self.ctx)
        }
    }

    /// Copy the state to a destination buffer
    /// Returns the number of bytes copied
    pub fn state_get_data(&self, dst: &mut [u8]) -> usize {
        unsafe {
            llama_state_get_data(self.ctx, dst.as_mut_ptr(), dst.len())
        }
    }

    /// Set the state from a source buffer
    /// Returns the number of bytes read
    pub fn state_set_data(&mut self, src: &[u8]) -> usize {
        unsafe {
            llama_state_set_data(self.ctx, src.as_ptr(), src.len())
        }
    }

    /// Load state from a session file
    /// Returns Ok((tokens, count)) on success, Err(()) on failure
    pub fn state_load_file<P: AsRef<std::path::Path>>(
        &mut self,
        path: P,
        token_capacity: usize
    ) -> Result<(Vec<Token>, usize), ()> {
        let path_str = path.as_ref().to_str()
            .ok_or(())?;
        let path_cstr = CString::new(path_str)
            .map_err(|_| ())?;

        let mut tokens = vec![0; token_capacity];
        let mut count = 0;

        let success = unsafe {
            llama_state_load_file(
                self.ctx,
                path_cstr.as_ptr(),
                tokens.as_mut_ptr(),
                token_capacity,
                &mut count
            )
        };

        if success {
            tokens.truncate(count);
            Ok((tokens, count))
        } else {
            Err(())
        }
    }

    /// Save state to a session file
    /// Returns true on success, false on failure
    pub fn state_save_file<P: AsRef<std::path::Path>>(
        &self,
        path: P,
        tokens: &[Token]
    ) -> bool {
        let path_str = path.as_ref().to_str()
            .unwrap_or("");
        let path_cstr = CString::new(path_str).unwrap();

        unsafe {
            llama_state_save_file(
                self.ctx,
                path_cstr.as_ptr(),
                tokens.as_ptr(),
                tokens.len()
            )
        }
    }

    /// Get the exact size needed to copy the KV cache of a single sequence
    pub fn state_seq_get_size(&self, seq_id: SeqId) -> usize {
        unsafe {
            llama_state_seq_get_size(self.ctx, seq_id)
        }
    }

    /// Copy the KV cache of a single sequence into the specified buffer
    /// Returns the number of bytes copied
    pub fn state_seq_get_data(&self, dst: &mut [u8], seq_id: SeqId) -> usize {
        unsafe {
            llama_state_seq_get_data(self.ctx, dst.as_mut_ptr(), dst.len(), seq_id)
        }
    }

    /// Copy sequence data into the specified sequence
    /// Returns Ok(bytes_read) if successful, Err(()) if failed to load
    pub fn state_seq_set_data(&mut self, src: &[u8], dest_seq_id: SeqId) -> Result<usize, ()> {
        let result = unsafe {
            llama_state_seq_set_data(self.ctx, src.as_ptr(), src.len(), dest_seq_id)
        };
        if result > 0 {
            Ok(result)
        } else {
            Err(())
        }
    }

    /// Save sequence state to a file
    /// Returns number of bytes written, 0 on failure
    pub fn state_seq_save_file<P: AsRef<std::path::Path>>(
        &self,
        filepath: P,
        seq_id: SeqId,
        tokens: &[Token],
    ) -> usize {
        let path_str = filepath.as_ref().to_str().unwrap_or("");
        let path_cstr = CString::new(path_str).unwrap();

        unsafe {
            llama_state_seq_save_file(
                self.ctx,
                path_cstr.as_ptr(),
                seq_id,
                tokens.as_ptr(),
                tokens.len()
            )
        }
    }

    /// Load sequence state from a file
    /// Returns Ok((tokens, count, bytes_read)) if successful, Err(()) if failed
    pub fn state_seq_load_file<P: AsRef<std::path::Path>>(
        &mut self,
        filepath: P,
        dest_seq_id: SeqId,
        token_capacity: usize,
    ) -> Result<(Vec<Token>, usize, usize), ()> {
        let path_str = filepath.as_ref().to_str().ok_or(())?;
        let path_cstr = CString::new(path_str).map_err(|_| ())?;

        let mut tokens = vec![0; token_capacity];
        let mut count = 0;

        let bytes_read = unsafe {
            llama_state_seq_load_file(
                self.ctx,
                path_cstr.as_ptr(),
                dest_seq_id,
                tokens.as_mut_ptr(),
                token_capacity,
                &mut count
            )
        };

        if bytes_read > 0 {
            tokens.truncate(count);
            Ok((tokens, count, bytes_read))
        } else {
            Err(())
        }
    }

    /// Get a batch for a single sequence of tokens
    /// The sequence ID will be fixed to 0
    /// The position of the tokens will be tracked automatically
    pub fn batch_get_one(&self, tokens: &mut [Token]) -> Batch {
        unsafe {
            Batch::from_c_batch(&llama_batch_get_one(tokens.as_mut_ptr(), tokens.len() as i32))
        }
    }

    /// Initialize a new batch of tokens
    /// If embd is non-zero, allocates embedding buffer instead of token buffer
    pub fn batch_init(n_tokens: i32, embd: i32, n_seq_max: i32) -> Batch {
        // Use the safer Rust-managed batch initialization
        Batch::batch_init(n_tokens, embd, n_seq_max)
    }

    /// Process a batch of tokens with the encoder
    /// Returns Ok(()) on success, Err(code) on error
    pub fn encode(&mut self, batch: Batch) -> Result<(), i32> {
        let c_batch = batch.into_c_batch();
        let result = unsafe {
            llama_encode(self.ctx, c_batch)
        };
        if result == 0 {
            Ok(())
        } else {
            Err(result)
        }
    }

    /// Process a batch of tokens with the decoder
    /// Returns:
    /// - Ok(0) on success
    /// - Ok(1) if could not find a KV slot for the batch (try reducing batch size or increasing context)
    /// - Err(code) on error, where code < 0. KV cache state is restored in this case.
    pub fn decode(&mut self, batch: Batch) -> Result<i32, i32> {
        let c_batch = batch.into_c_batch();
        let result = unsafe {
            llama_decode(self.ctx, c_batch)
        };
        if result >= 0 {
            Ok(result)
        } else {
            Err(result)
        }
    }

    /// Set the number of threads used for decoding
    /// n_threads: number of threads for single token generation
    /// n_threads_batch: number of threads for prompt/batch processing
    pub fn set_n_threads(&mut self, n_threads: i32, n_threads_batch: i32) {
        unsafe {
            llama_set_n_threads(self.ctx, n_threads, n_threads_batch);
        }
    }

    /// Get the number of threads used for single token generation
    pub fn n_threads(&self) -> i32 {
        unsafe {
            llama_n_threads(self.ctx)
        }
    }

    /// Get the number of threads used for prompt/batch processing
    pub fn n_threads_batch(&self) -> i32 {
        unsafe {
            llama_n_threads_batch(self.ctx)
        }
    }

    /// Set whether the model is in embeddings mode
    /// If true, embeddings will be returned but logits will not
    pub fn set_embeddings(&mut self, embeddings: bool) {
        unsafe {
            llama_set_embeddings(self.ctx, embeddings);
        }
    }

    /// Set whether to use causal attention
    /// If true, the model will only attend to past tokens
    pub fn set_causal_attn(&mut self, causal_attn: bool) {
        unsafe {
            llama_set_causal_attn(self.ctx, causal_attn);
        }
    }

    /// Set abort callback for computation
    pub unsafe fn set_abort_callback(&mut self, callback: ggml_abort_callback, callback_data: *mut std::ffi::c_void) {
        llama_set_abort_callback(self.ctx, callback, callback_data);
    }

    /// Wait until all computations are finished
    /// This is automatically done when getting computation results
    /// and usually not necessary to call explicitly
    pub fn synchronize(&mut self) {
        unsafe {
            llama_synchronize(self.ctx);
        }
    }

    /// Get token logits from the last decode call
    /// Returns a slice containing logits for tokens where llama_batch.logits[i] != 0
    /// The logits are stored contiguously in the order they appeared in the batch
    /// Shape: [n_tokens_with_logits, n_vocab]
    pub fn get_logits(&self) -> &[f32] {
        unsafe {
            let logits_ptr = llama_get_logits(self.ctx);
            if logits_ptr.is_null() {
                &[]
            } else {
                // Calculate slice length: n_tokens_with_logits * n_vocab
                // For safety, we'll need to track this in the context
                let n_vocab = self.n_vocab();
                std::slice::from_raw_parts(logits_ptr, n_vocab as usize)
            }
        }
    }

    /// Get logits for a specific token index
    /// For positive indices, equivalent to: get_logits() + output_ids[i]*n_vocab
    /// Negative indices access logits in reverse order (-1 = last logit)
    /// Returns None for invalid indices
    pub fn get_logits_ith(&self, i: i32) -> Option<&[f32]> {
        unsafe {
            let logits_ptr = llama_get_logits_ith(self.ctx, i);
            if logits_ptr.is_null() {
                None
            } else {
                let n_vocab = self.n_vocab();
                Some(std::slice::from_raw_parts(logits_ptr, n_vocab as usize))
            }
        }
    }

    /// Get all output token embeddings
    /// When pooling_type is None or using a generative model,
    /// returns embeddings for tokens where llama_batch.logits[i] != 0
    /// Shape: [n_outputs * n_embd]
    /// Returns None if no embeddings are available
    pub fn get_embeddings(&self) -> Option<&[f32]> {
        unsafe {
            let embeddings_ptr = llama_get_embeddings(self.ctx);
            if embeddings_ptr.is_null() {
                None
            } else {
                let n_embd = self.model_n_embd();
                Some(std::slice::from_raw_parts(embeddings_ptr, n_embd as usize))
            }
        }
    }

    /// Get embeddings for a specific token index
    /// For positive indices, equivalent to: get_embeddings() + output_ids[i]*n_embd
    /// Negative indices access embeddings in reverse order (-1 = last embedding)
    /// Shape: [n_embd] (1-dimensional)
    /// Returns None for invalid indices
    pub fn get_embeddings_ith(&self, i: i32) -> Option<&[f32]> {
        unsafe {
            let embeddings_ptr = llama_get_embeddings_ith(self.ctx, i);
            if embeddings_ptr.is_null() {
                None
            } else {
                let n_embd = self.model_n_embd();
                Some(std::slice::from_raw_parts(embeddings_ptr, n_embd as usize))
            }
        }
    }

    /// Get embeddings for a sequence ID
    /// Returns:
    /// - None if pooling_type is None
    /// - [1] float array with sequence rank if pooling_type is Rank
    /// - [n_embd] float array (1-dimensional) otherwise
    pub fn get_embeddings_seq(&self, seq_id: SeqId) -> Option<&[f32]> {
        unsafe {
            let embeddings_ptr = llama_get_embeddings_seq(self.ctx, seq_id);
            if embeddings_ptr.is_null() {
                None
            } else {
                let len = match self.pooling_type() {
                    PoolingType::None => return None,
                    PoolingType::Rank => 1,
                    _ => self.model_n_embd(),
                };
                Some(std::slice::from_raw_parts(embeddings_ptr, len as usize))
            }
        }
    }

    /// Apply chat template to format a conversation
    /// Both model and custom_template are optional, but at least one is required
    /// custom_template has higher precedence than model's default template
    /// Returns Ok(formatted_prompt) on success, Err(needed_size) if buffer was too small
    pub fn chat_apply_template(
        &self,
        template: Option<&str>,
        messages: &[ChatMessage],
        add_assistant: bool,
        buf_size: usize
    ) -> Result<String, i32> {
        // Convert template to C string if provided
        let template_cstr = template.map(|t| CString::new(t).unwrap());
        let template_ptr = template_cstr.as_ref().map_or(std::ptr::null(), |cs| cs.as_ptr());

        // Convert messages to C representation
        let c_messages: Vec<llama_chat_message> = messages.iter().map(|msg| {
            let role = CString::new(msg.role.as_str()).unwrap();
            let content = CString::new(msg.content.as_str()).unwrap();
            llama_chat_message {
                role: role.into_raw(),
                content: content.into_raw(),
            }
        }).collect();

        // Allocate buffer for output
        let mut buf = vec![0i8; buf_size];

        let result = unsafe {
            llama_chat_apply_template(
                template_ptr,
                c_messages.as_ptr(),
                messages.len(),
                add_assistant,
                buf.as_mut_ptr(),
                buf_size as i32
            )
        };

        if result < 0 {
            return Err(result);
        }

        // Convert buffer to string
        let c_str = unsafe { CStr::from_ptr(buf.as_ptr()) };
        Ok(c_str.to_string_lossy().into_owned())
    }

    /// Get list of built-in chat templates
    /// Returns Ok(templates) on success, Err(()) on failure
    pub fn chat_builtin_templates(&self) -> Result<Vec<String>, ()> {
        let mut templates = Vec::new();
        let mut output: Vec<*const i8> = Vec::new();

        // First call to get required length
        let len = unsafe {
            llama_chat_builtin_templates(std::ptr::null_mut(), 0)
        };

        if len <= 0 {
            return Err(());
        }

        // Allocate space for template pointers
        output.resize(len as usize, std::ptr::null());

        // Get actual templates
        let result = unsafe {
            llama_chat_builtin_templates(output.as_mut_ptr() as *mut *const i8, len as usize)
        };

        if result != len {
            return Err(());
        }

        // Convert C strings to Rust strings
        for &ptr in output.iter() {
            if !ptr.is_null() {
                let c_str = unsafe { CStr::from_ptr(ptr) };
                templates.push(c_str.to_string_lossy().into_owned());
            }
        }

        Ok(templates)
    }

    /// Get performance data for context operations
    pub fn perf_context(&self) -> PerfContextData {
        unsafe {
            llama_perf_context(self.ctx).into()
        }
    }

    /// Print performance data for context operations
    pub fn perf_context_print(&self) {
        unsafe {
            llama_perf_context_print(self.ctx);
        }
    }

    /// Reset performance counters for context operations
    pub fn perf_context_reset(&mut self) {
        unsafe {
            llama_perf_context_reset(self.ctx);
        }
    }
}

/// Implements automatic resource cleanup for Llama struct when it goes out of scope
///
/// Ensures proper cleanup of C resources:
/// - Frees the llama context (ctx) using llama_free()
/// - Frees the llama model using llama_model_free()
///
/// This implementation prevents memory leaks by automatically deallocating
/// resources when a Llama instance is dropped. The unsafe block is required
/// for calling the C deallocation functions.
impl Drop for Llama {
    fn drop(&mut self) {
        unsafe {
            llama_free(self.ctx);
            llama_model_free(self.model);
        }
    }
}

// Implement Send and Sync for Llama since the underlying C library is thread-safe
unsafe impl Send for Llama {}
unsafe impl Sync for Llama {}

/// Model key-value override types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ModelKvOverrideType {
    /// Integer value override (0)
    Int,
    /// Float value override (1)
    Float,
    /// Boolean value override (2)
    Bool,
    /// String value override (3)
    Str,
}

/// Converts from C llama_model_kv_override_type enum to Rust ModelKvOverrideType enum
///
/// Maps model key-value override types:
/// - Int -> LLAMA_KV_OVERRIDE_TYPE_INT: Integer value override
/// - Float -> LLAMA_KV_OVERRIDE_TYPE_FLOAT: Floating point value override
/// - Bool -> LLAMA_KV_OVERRIDE_TYPE_BOOL: Boolean value override
/// - Str -> LLAMA_KV_OVERRIDE_TYPE_STR: String value override
///
/// Will panic if an invalid override type is provided
impl From<llama_model_kv_override_type> for ModelKvOverrideType {
    fn from(override_type: llama_model_kv_override_type) -> Self {
        match override_type {
            llama_model_kv_override_type_LLAMA_KV_OVERRIDE_TYPE_INT => ModelKvOverrideType::Int,
            llama_model_kv_override_type_LLAMA_KV_OVERRIDE_TYPE_FLOAT => ModelKvOverrideType::Float,
            llama_model_kv_override_type_LLAMA_KV_OVERRIDE_TYPE_BOOL => ModelKvOverrideType::Bool,
            llama_model_kv_override_type_LLAMA_KV_OVERRIDE_TYPE_STR => ModelKvOverrideType::Str,
            _ => panic!("Invalid model KV override type"),
        }
    }
}

/// Converts from Rust ModelKvOverrideType enum to C llama_model_kv_override_type enum
///
/// Maps model key-value override types back to their C representation:
/// - Int -> LLAMA_KV_OVERRIDE_TYPE_INT: Integer value override
/// - Float -> LLAMA_KV_OVERRIDE_TYPE_FLOAT: Floating point value override
/// - Bool -> LLAMA_KV_OVERRIDE_TYPE_BOOL: Boolean value override
/// - Str -> LLAMA_KV_OVERRIDE_TYPE_STR: String value override
impl From<ModelKvOverrideType> for llama_model_kv_override_type {
    fn from(override_type: ModelKvOverrideType) -> Self {
        match override_type {
            ModelKvOverrideType::Int => llama_model_kv_override_type_LLAMA_KV_OVERRIDE_TYPE_INT,
            ModelKvOverrideType::Float => llama_model_kv_override_type_LLAMA_KV_OVERRIDE_TYPE_FLOAT,
            ModelKvOverrideType::Bool => llama_model_kv_override_type_LLAMA_KV_OVERRIDE_TYPE_BOOL,
            ModelKvOverrideType::Str => llama_model_kv_override_type_LLAMA_KV_OVERRIDE_TYPE_STR,
        }
    }
}

/// Model key-value override for configuring model parameters
#[derive(Debug, Clone)]
pub struct ModelKvOverride {
    /// Type of the override value
    pub tag: ModelKvOverrideType,
    /// Key to override (max 127 chars + null terminator)
    pub key: String,
    /// Override value
    pub value: ModelKvOverrideValue,
}

/// Possible values for a model key-value override
#[derive(Debug, Clone)]
pub enum ModelKvOverrideValue {
    /// 64-bit integer value
    Int(i64),
    /// 64-bit float value
    Float(f64),
    /// Boolean value
    Bool(bool),
    /// String value (max 127 chars + null terminator)
    Str(String),
}

impl ModelKvOverride {
    /// Create a new model key-value override
    pub fn new(key: impl Into<String>, value: ModelKvOverrideValue) -> Self {
        let key = key.into();
        if key.len() >= 128 {
            panic!("Key length must be less than 128 characters");
        }

        let tag = match value {
            ModelKvOverrideValue::Int(_) => ModelKvOverrideType::Int,
            ModelKvOverrideValue::Float(_) => ModelKvOverrideType::Float,
            ModelKvOverrideValue::Bool(_) => ModelKvOverrideType::Bool,
            ModelKvOverrideValue::Str(ref s) => {
                if s.len() >= 128 {
                    panic!("String value length must be less than 128 characters");
                }
                ModelKvOverrideType::Str
            }
        };

        Self { tag, key, value }
    }

    /// Convert to C representation
    pub fn into_c_override(self) -> llama_model_kv_override {
        let mut key = [0i8; 128];
        for (i, b) in self.key.bytes().take(127).enumerate() {
            key[i] = b as i8;
        }

        let mut result = llama_model_kv_override {
            tag: self.tag.into(),
            key,
            __bindgen_anon_1: unsafe { std::mem::zeroed() },
        };

        match self.value {
            ModelKvOverrideValue::Int(val) => {
                result.__bindgen_anon_1.val_i64 = val;
            },
            ModelKvOverrideValue::Float(val) => {
                result.__bindgen_anon_1.val_f64 = val;
            },
            ModelKvOverrideValue::Bool(val) => {
                result.__bindgen_anon_1.val_bool = val;
            },
            ModelKvOverrideValue::Str(val) => {
                let mut val_str = [0i8; 128];
                for (i, b) in val.bytes().take(127).enumerate() {
                    val_str[i] = b as i8;
                }
                result.__bindgen_anon_1.val_str = val_str;
            },
        }

        result
    }

    /// Create from C representation
    pub unsafe fn from_c_override(override_: &llama_model_kv_override) -> Self {
        let key = std::ffi::CStr::from_ptr(override_.key.as_ptr())
            .to_string_lossy()
            .into_owned();

        let value = match ModelKvOverrideType::from(override_.tag) {
            ModelKvOverrideType::Int => {
                ModelKvOverrideValue::Int(override_.__bindgen_anon_1.val_i64)
            }
            ModelKvOverrideType::Float => {
                ModelKvOverrideValue::Float(override_.__bindgen_anon_1.val_f64)
            }
            ModelKvOverrideType::Bool => {
                ModelKvOverrideValue::Bool(override_.__bindgen_anon_1.val_bool)
            }
            ModelKvOverrideType::Str => {
                let s = std::ffi::CStr::from_ptr(override_.__bindgen_anon_1.val_str.as_ptr())
                    .to_string_lossy()
                    .into_owned();
                ModelKvOverrideValue::Str(s)
            }
        };

        Self {
            tag: override_.tag.into(),
            key,
            value,
        }
    }
}

/// Parameters for loading a model
#[derive(Debug, Clone)]
pub struct ModelParams {
    /// List of devices to use for offloading (None = use all available devices)
    pub devices: Option<Vec<GgmlBackendDev>>,
    /// Number of layers to store in VRAM
    pub n_gpu_layers: i32,
    /// How to split the model across multiple GPUs
    pub split_mode: SplitMode,
    /// The GPU that is used for the entire model when split_mode is None
    pub main_gpu: i32,
    /// Proportion of the model (layers or rows) to offload to each GPU
    pub tensor_split: Option<Vec<f32>>,
    /// Progress callback function
    pub progress_callback: ProgressCallback,
    /// Context pointer passed to the progress callback
    pub progress_callback_user_data: Option<*mut std::ffi::c_void>,
    /// Override key-value pairs of the model meta data
    pub kv_overrides: Option<Vec<ModelKvOverride>>,
    /// Only load the vocabulary, no weights
    pub vocab_only: bool,
    /// Use mmap if possible
    pub use_mmap: bool,
    /// Force system to keep model in RAM
    pub use_mlock: bool,
    /// Validate model tensor data
    pub check_tensors: bool,
}

/// Implements the Default trait for ModelParams struct
///
/// Creates default model parameters by:
/// - Calling the C llama_model_default_params() function
/// - Converting the C parameters to Rust ModelParams struct
///
/// Uses unsafe block since it calls C code:
/// - safe because llama_model_default_params() is thread-safe
/// - returns stable default values defined by llama.cpp
impl Default for ModelParams {
    fn default() -> Self {
        unsafe { Self::from_c_params(&llama_model_default_params()) }
    }
}

impl ModelParams {
    /// Convert to C representation
    pub fn into_c_params(self) -> llama_model_params {
        let devices = self.devices.as_ref().map_or(std::ptr::null_mut(), |devices| {
            let mut dev_ptrs: Vec<_> = devices.iter().map(|&d| d).collect();
            dev_ptrs.push(std::ptr::null_mut()); // Null terminate the list
            Box::into_raw(dev_ptrs.into_boxed_slice()) as *mut _
        });

        let tensor_split = self.tensor_split.as_ref().map_or(std::ptr::null(), |split| {
            split.as_ptr()
        });

        let kv_overrides = self.kv_overrides.as_ref().map_or(std::ptr::null(), |overrides| {
            let c_overrides: Vec<_> = overrides.iter().map(|o| o.clone().into_c_override()).collect();
            Box::into_raw(c_overrides.into_boxed_slice()) as *const _
        });

        llama_model_params {
            devices,
            tensor_buft_overrides: std::ptr::null(), // Field 2 - NEW field in updated llama.cpp
            n_gpu_layers: self.n_gpu_layers,
            split_mode: self.split_mode.into(),
            main_gpu: self.main_gpu,
            tensor_split,
            progress_callback: self.progress_callback,
            progress_callback_user_data: self.progress_callback_user_data.unwrap_or(std::ptr::null_mut()),
            kv_overrides,
            vocab_only: self.vocab_only,
            use_mmap: self.use_mmap,
            use_mlock: self.use_mlock,
            check_tensors: self.check_tensors,
        }
    }

    /// Create from C representation
    pub unsafe fn from_c_params(params: &llama_model_params) -> Self {
        let devices = if params.devices.is_null() {
            None
        } else {
            let mut devices = Vec::new();
            let mut ptr = params.devices;
            while !(*ptr).is_null() {
                devices.push(*ptr);
                ptr = ptr.add(1);
            }
            Some(devices)
        };

        let tensor_split = if params.tensor_split.is_null() {
            None
        } else {
            let n_devices = llama_max_devices();
            let slice = std::slice::from_raw_parts(params.tensor_split, n_devices as usize);
            Some(slice.to_vec())
        };

        let kv_overrides = if params.kv_overrides.is_null() {
            None
        } else {
            let mut overrides = Vec::new();
            let mut ptr = params.kv_overrides;
            while !(*ptr).key[0] == 0 {
                overrides.push(ModelKvOverride::from_c_override(&*ptr));
                ptr = ptr.add(1);
            }
            Some(overrides)
        };

        Self {
            devices,
            n_gpu_layers: params.n_gpu_layers,
            split_mode: params.split_mode.into(),
            main_gpu: params.main_gpu,
            tensor_split,
            progress_callback: params.progress_callback,
            progress_callback_user_data: if params.progress_callback_user_data.is_null() {
                None
            } else {
                Some(params.progress_callback_user_data)
            },
            kv_overrides,
            vocab_only: params.vocab_only,
            use_mmap: params.use_mmap,
            use_mlock: params.use_mlock,
            check_tensors: params.check_tensors,
        }
    }
}


/// Converts from Rust ModelParams struct to C llama_model_params struct
///
/// Handles complex conversion of model parameters to C representation:
/// - devices: Converts device list to null-terminated array of pointers
/// - n_gpu_layers: Number of layers to offload to GPU
/// - split_mode: Converts enum for model parallel splitting strategy
/// - main_gpu: Primary GPU device index
/// - tensor_split: Converts optional tensor split configuration
/// - progress_callback: Function pointer for loading progress updates
/// - progress_callback_user_data: Optional user data for callback
/// - kv_overrides: Converts optional key-value override parameters
/// - vocab_only: Flag to load only vocabulary
/// - use_mmap: Flag to use memory mapping
/// - use_mlock: Flag to lock memory
/// - check_tensors: Flag to validate tensor data
///
/// # Safety
/// This implementation handles several unsafe operations:
/// - Raw pointer creation and management
/// - C-compatible memory layout conversions
/// - Null termination of device lists
/// - Callback function pointer handling
impl From<ModelParams> for llama_model_params {
    fn from(params: ModelParams) -> Self {
        // Convert devices to raw pointers
        let devices_ptr = params.devices.as_ref().map_or(std::ptr::null_mut(), |devices| {
            let mut dev_ptrs: Vec<_> = devices.iter().map(|&d| d).collect();
            dev_ptrs.push(std::ptr::null_mut()); // Null terminate the list
            Box::into_raw(dev_ptrs.into_boxed_slice()) as *mut _
        });

        // Convert tensor split
        let tensor_split_ptr = params.tensor_split.as_ref().map_or(std::ptr::null(), |split| {
            split.as_ptr()
        });

        // Convert KV overrides
        let kv_overrides_ptr = params.kv_overrides.as_ref().map_or(std::ptr::null(), |overrides| {
            let c_overrides: Vec<_> = overrides.iter().map(|o| o.clone().into_c_override()).collect();
            Box::into_raw(c_overrides.into_boxed_slice()) as *const _
        });

        llama_model_params {
            devices: devices_ptr,
            n_gpu_layers: params.n_gpu_layers,
            split_mode: params.split_mode.into(),
            main_gpu: params.main_gpu,
            tensor_split: tensor_split_ptr,
            tensor_buft_overrides: std::ptr::null(), // New field in updated llama.cpp
            progress_callback: params.progress_callback,
            progress_callback_user_data: params.progress_callback_user_data.unwrap_or(std::ptr::null_mut()),
            kv_overrides: kv_overrides_ptr,
            vocab_only: params.vocab_only,
            use_mmap: params.use_mmap,
            use_mlock: params.use_mlock,
            check_tensors: params.check_tensors,
        }
    }
}

/// Converts from C llama_model_params struct to Rust ModelParams struct
///
/// Provides safe conversion of model parameters:
/// - Takes a C llama_model_params struct
/// - Uses from_c_params() to convert to Rust representation
/// - Unsafe block needed for C struct reference handling
///
/// Used when receiving model parameters from llama.cpp functions
impl From<llama_model_params> for ModelParams {
    fn from(params: llama_model_params) -> Self {
        unsafe {
            Self::from_c_params(&params)
        }
    }
}

/// Parameters for model quantization
#[derive(Debug, Clone)]
pub struct ModelQuantizeParams {
    /// Number of threads to use for quantizing, if <=0 will use std::thread::hardware_concurrency()
    pub n_thread: i32,
    /// Quantize to this file type
    pub ftype: FileType,
    /// Output tensor type
    pub output_tensor_type: ggml_type,
    /// Token embeddings tensor type
    pub token_embedding_type: ggml_type,
    /// Allow quantizing non-f32/f16 tensors
    pub allow_requantize: bool,
    /// Quantize output.weight
    pub quantize_output_tensor: bool,
    /// Only copy tensors - ftype, allow_requantize and quantize_output_tensor are ignored
    pub only_copy: bool,
    /// Quantize all tensors to the default type
    pub pure_: bool,
    /// Quantize to the same number of shards
    pub keep_split: bool,
    /// Pointer to importance matrix data
    pub imatrix: Option<*mut std::ffi::c_void>,
    /// Pointer to vector containing overrides
    pub kv_overrides: Option<*mut std::ffi::c_void>,
}

/// Implements the Default trait for ModelQuantizeParams struct
///
/// Creates default model quantization parameters by:
/// - Calling the C llama_model_quantize_default_params() function
/// - Converting the C parameters to Rust ModelQuantizeParams struct
///
/// Uses unsafe block since it calls C code:
/// - Safe because llama_model_quantize_default_params() is thread-safe
/// - Returns stable default quantization settings defined by llama.cpp
impl Default for ModelQuantizeParams {
    fn default() -> Self {
        unsafe { Self::from_c_params(llama_model_quantize_default_params()) }
    }
}

impl ModelQuantizeParams {
    /// Convert to C representation
    pub fn into_c_params(self) -> llama_model_quantize_params {
        llama_model_quantize_params {
            nthread: self.n_thread,
            ftype: self.ftype.into(),
            output_tensor_type: self.output_tensor_type,
            token_embedding_type: self.token_embedding_type,
            allow_requantize: self.allow_requantize,
            quantize_output_tensor: self.quantize_output_tensor,
            only_copy: self.only_copy,
            pure_: self.pure_,
            keep_split: self.keep_split,
            imatrix: self.imatrix.unwrap_or(std::ptr::null_mut()),
            kv_overrides: self.kv_overrides.unwrap_or(std::ptr::null_mut()),
            prune_layers: std::ptr::null_mut(), // New field in updated llama.cpp
            tensor_types: std::ptr::null_mut(), // New field in updated llama.cpp
        }
    }

    /// Create from C representation
    pub unsafe fn from_c_params(params: llama_model_quantize_params) -> Self {
        Self {
            n_thread: params.nthread,
            ftype: params.ftype.into(),
            output_tensor_type: params.output_tensor_type,
            token_embedding_type: params.token_embedding_type,
            allow_requantize: params.allow_requantize,
            quantize_output_tensor: params.quantize_output_tensor,
            only_copy: params.only_copy,
            pure_: params.pure_,
            keep_split: params.keep_split,
            imatrix: if params.imatrix.is_null() { None } else { Some(params.imatrix) },
            kv_overrides: if params.kv_overrides.is_null() { None } else { Some(params.kv_overrides) },
        }
    }
}

/// Converts from C llama_model_quantize_params struct to Rust ModelQuantizeParams struct
///
/// Provides safe conversion of model quantization parameters:
/// - Takes a C llama_model_quantize_params struct
/// - Uses from_c_params() to convert to Rust representation
/// - Unsafe block needed for C struct handling
///
/// Used when receiving quantization parameters from llama.cpp functions
impl From<llama_model_quantize_params> for ModelQuantizeParams {
    fn from(params: llama_model_quantize_params) -> Self {
        unsafe { Self::from_c_params(params) }
    }
}

/// Represents a token-specific bias to be applied during sampling
#[derive(Debug, Clone, Copy)]
pub struct LogitBias {
    /// The token to bias
    pub token: Token,
    /// The bias value to apply
    pub bias: f32,
}

/// Converts from C llama_logit_bias struct to Rust LogitBias struct
///
/// Maps token bias data between C and Rust representations:
/// - token: Token identifier to apply bias to
/// - bias: Bias value to add to token's logit (positive increases likelihood, negative decreases)
impl From<llama_logit_bias> for LogitBias {
    fn from(bias: llama_logit_bias) -> Self {
        Self {
            token: bias.token,
            bias: bias.bias,
        }
    }
}


/// Converts from Rust LogitBias struct to C llama_logit_bias struct
///
/// Maps token bias data back to C representation:
/// - token: Token identifier to apply bias to
/// - bias: Bias value to add to token's logit (positive increases likelihood, negative decreases)
///
/// Used when passing token biases to llama.cpp sampling functions
impl From<LogitBias> for llama_logit_bias {
    fn from(bias: LogitBias) -> Self {
        Self {
            token: bias.token,
            bias: bias.bias,
        }
    }
}

/// Parameters for sampler chain configuration
pub struct SamplerChainParams {
    /// Whether to measure performance timings
    pub no_perf: bool,
}

/// Converts from C llama_sampler_chain_params struct to Rust SamplerChainParams struct
///
/// Maps sampler chain parameters:
/// - no_perf: Boolean flag to disable performance measurements in the sampling chain
///
/// Used when receiving sampler chain configuration from llama.cpp functions
impl From<llama_sampler_chain_params> for SamplerChainParams {
    fn from(params: llama_sampler_chain_params) -> Self {
        Self {
            no_perf: params.no_perf,
        }
    }
}


/// Converts from Rust SamplerChainParams struct to C llama_sampler_chain_params struct
///
/// Maps sampler chain parameters back to C representation:
/// - no_perf: Boolean flag to disable performance measurements in the sampling chain
///
/// Used when passing sampler chain configuration to llama.cpp functions
impl From<SamplerChainParams> for llama_sampler_chain_params {
    fn from(params: SamplerChainParams) -> Self {
        Self {
            no_perf: params.no_perf,
        }
    }
}


/// Implements the Default trait for SamplerChainParams struct
///
/// Creates default sampler chain parameters by:
/// - Calling the C llama_sampler_chain_default_params() function
/// - Converting the C parameters to Rust SamplerChainParams struct via into()
///
/// Uses unsafe block since it calls C code:
/// - Safe because llama_sampler_chain_default_params() is thread-safe
/// - Returns stable default sampling configuration defined by llama.cpp
impl Default for SamplerChainParams {
    fn default() -> Self {
        // Use the C default params function
        unsafe {
            llama_sampler_chain_default_params().into()
        }
    }
}

/// A message in a chat conversation, used in chat templates
pub struct ChatMessage {
    /// The role of the message sender (e.g., "user", "assistant")
    pub role: String,
    /// The content of the message
    pub content: String,
}


/// Converts from C llama_chat_message struct to Rust ChatMessage struct
///
/// Maps chat message data between C and Rust representations:
/// - role: Converts C string pointer to Rust String (e.g. "user", "assistant", "system")
/// - content: Converts C string pointer to Rust String containing the message text
///
/// Uses unsafe block for C string handling:
/// - Safe because it only reads from valid C string pointers
/// - Handles UTF-8 conversion via to_string_lossy() for robustness
impl From<llama_chat_message> for ChatMessage {
    fn from(msg: llama_chat_message) -> Self {
        unsafe {
            Self {
                role: CStr::from_ptr(msg.role).to_string_lossy().into_owned(),
                content: CStr::from_ptr(msg.content).to_string_lossy().into_owned(),
            }
        }
    }
}


/// Converts from Rust ChatMessage struct to C llama_chat_message struct
///
/// Maps chat message data back to C representation:
/// - role: Converts Rust String to C string pointer (e.g. "user", "assistant", "system")
/// - content: Converts Rust String to C string pointer containing the message text
///
/// Note: This implementation transfers ownership of the C strings to the caller.
/// The caller is responsible for properly freeing the memory using CString::from_raw()
/// to avoid memory leaks.
impl From<ChatMessage> for llama_chat_message {
    fn from(msg: ChatMessage) -> Self {
        let role = CString::new(msg.role).unwrap();
        let content = CString::new(msg.content).unwrap();
        Self {
            role: role.into_raw(),
            content: content.into_raw(),
        }
    }
}

/// Safe wrapper around llama_adapter_lora pointer
pub struct AdapterLora {
    ptr: NonNull<llama_adapter_lora>,
}

// Implement Send and Sync for AdapterLora since the underlying C library is thread-safe
unsafe impl Send for AdapterLora {}
unsafe impl Sync for AdapterLora {}

impl AdapterLora {
    pub fn as_ptr(&self) -> *mut llama_adapter_lora {
        self.ptr.as_ptr()
    }

    /// Initialize a LoRA adapter from a file
    pub fn init(model: &Model, path: &str) -> Option<Self> {
        let path_cstr = CString::new(path).ok()?;
        let ptr = unsafe { llama_adapter_lora_init(model.as_ptr(), path_cstr.as_ptr()) };
        NonNull::new(ptr).map(|ptr| Self { ptr })
    }

    /// Set this adapter in the given context with the specified scale
    pub fn set_in_context(&self, ctx: &Context, scale: f32) -> bool {
        unsafe { llama_set_adapter_lora(ctx.as_ptr(), self.as_ptr(), scale) == 0 }
    }

    /// Remove this adapter from the given context
    pub fn remove_from_context(&self, ctx: &Context) -> bool {
        unsafe { llama_rm_adapter_lora(ctx.as_ptr(), self.as_ptr()) == 0 }
    }
}


/// Implements automatic resource cleanup for AdapterLora struct when it goes out of scope
///
/// Ensures proper cleanup of LoRA adapter resources:
/// - Calls llama_adapter_lora_free() with the adapter pointer
/// - Automatically triggered when AdapterLora instance is dropped
///
/// Uses unsafe block since it calls C code:
/// - Safe because it only frees memory associated with this adapter
/// - Prevents memory leaks from LoRA adapter allocations
impl Drop for AdapterLora {
    fn drop(&mut self) {
        unsafe { llama_adapter_lora_free(self.as_ptr()) }
    }
}

/// Information associated with an individual cell in the KV cache view
#[derive(Debug, Clone, Copy)]
pub struct KvCacheViewCell {
    /// The position for this cell. Takes KV cache shifts into account.
    /// May be negative if the cell is not populated.
    pub pos: Pos,
}


// NOTE: llama_kv_cache_view_cell was removed in the latest llama.cpp
// These conversions are no longer available
//
// /// Converts from C llama_kv_cache_view_cell struct to Rust KvCacheViewCell struct
// impl From<llama_kv_cache_view_cell> for KvCacheViewCell {
//     fn from(cell: llama_kv_cache_view_cell) -> Self {
//         Self {
//             pos: cell.pos,
//         }
//     }
// }
//
// /// Converts from Rust KvCacheViewCell struct to C llama_kv_cache_view_cell struct
// impl From<KvCacheViewCell> for llama_kv_cache_view_cell {
//     fn from(cell: KvCacheViewCell) -> Self {
//         Self {
//             pos: cell.pos,
//         }
//     }
// }

/// An updateable view of the KV cache
#[derive(Debug)]
pub struct KvCacheView {
    /// Number of KV cache cells. This will be the same as the context size.
    pub n_cells: i32,
    /// Maximum number of sequences that can exist in a cell
    pub n_seq_max: i32,
    /// Number of tokens in the cache
    pub token_count: i32,
    /// Number of populated cache cells
    pub used_cells: i32,
    /// Maximum contiguous empty slots in the cache
    pub max_contiguous: i32,
    /// Index to the start of the max_contiguous slot range
    pub max_contiguous_idx: i32,
    /// Information for individual cells
    pub cells: Vec<KvCacheViewCell>,
    /// The sequences for each cell
    pub cells_sequences: Vec<SeqId>,
}

impl KvCacheView {
    /// Initialize a new KV cache view for the given context
    /// NOTE: This functionality is disabled as llama_kv_cache_view was removed in latest llama.cpp
    pub fn init(_ctx: &Context, _n_seq_max: i32) -> Self {
        // Return empty view since the C API was removed
        Self {
            n_cells: 0,
            n_seq_max: 0,
            token_count: 0,
            used_cells: 0,
            max_contiguous: 0,
            max_contiguous_idx: 0,
            cells: Vec::new(),
            cells_sequences: Vec::new(),
        }
    }

    /// Update the KV cache view with current state
    /// NOTE: This functionality is disabled as llama_kv_cache_view was removed in latest llama.cpp
    pub fn update(&mut self, _ctx: &Context) {
        // No-op since the C API was removed
    }

    /// Convert from C representation
    /// NOTE: This functionality is disabled as llama_kv_cache_view was removed in latest llama.cpp
    unsafe fn from_c_view(_view: ()) -> Self {
        // Return empty view since the C API was removed
        Self {
            n_cells: 0,
            n_seq_max: 0,
            token_count: 0,
            used_cells: 0,
            max_contiguous: 0,
            max_contiguous_idx: 0,
            cells: Vec::new(),
            cells_sequences: Vec::new(),
        }
    }

    /// Convert to C representation
    /// NOTE: This functionality is disabled as llama_kv_cache_view was removed in latest llama.cpp
    fn to_c_view(&self) -> () {
        // Return unit type since the C API was removed
        ()
    }
}


/// Implements automatic resource cleanup for KvCacheView struct when it goes out of scope
/// NOTE: This functionality is disabled as llama_kv_cache_view was removed in latest llama.cpp
impl Drop for KvCacheView {
    fn drop(&mut self) {
        // No-op since the C API was removed
    }
}

/// Context type for samplers
pub type SamplerContext = *mut std::ffi::c_void;

/// Token data array for sampling
#[derive(Debug)]
pub struct TokenDataArray {
    /// Array of token data
    pub data: Vec<TokenData>,
    /// Size of the array
    pub size: usize,
    /// Index of selected token (-1 if not selected)
    pub selected: i64,
    /// Whether the array is sorted by probability
    pub sorted: bool,
}

/// Provides mutable access to the underlying C llama_token_data_array struct
///
/// Creates and returns a pointer to a C-compatible token data array:
/// - data: Converts Rust Vec pointer to C array pointer of token data
/// - size: Total number of tokens in the array
/// - selected: Number of tokens selected/filtered
/// - sorted: Whether tokens are sorted by score
///
/// # Safety
/// This method requires unsafe usage since it:
/// - Creates raw pointers for C interop
/// - Caller must ensure pointer remains valid while in use
/// - Used internally by sampling functions
impl TokenDataArray {
    pub fn as_mut_ptr(&mut self) -> *mut llama_token_data_array {
        &mut llama_token_data_array {
            data: self.data.as_mut_ptr() as *mut llama_token_data,
            size: self.size,
            selected: self.selected,
            sorted: self.sorted,
        } as *mut _
    }
}

/// Function pointer types for sampler interface
type SamplerNameFn = unsafe extern "C" fn(*const llama_sampler) -> *const c_char;
type SamplerAcceptFn = unsafe extern "C" fn(*mut llama_sampler, Token);
type SamplerApplyFn = unsafe extern "C" fn(*mut llama_sampler, *mut llama_token_data_array);
type SamplerResetFn = unsafe extern "C" fn(*mut llama_sampler);
type SamplerCloneFn = unsafe extern "C" fn(*const llama_sampler) -> *mut llama_sampler;
type SamplerFreeFn = unsafe extern "C" fn(*mut llama_sampler);

/// Interface for implementing custom samplers
#[derive(Clone)]
pub struct SamplerInterface {
    /// Get the name of the sampler (optional)
    pub name: Option<SamplerNameFn>,
    /// Accept a token, updating internal state (optional)
    pub accept: Option<SamplerAcceptFn>,
    /// Apply sampling to token probabilities (required)
    pub apply: Option<SamplerApplyFn>,
    /// Reset sampler state (optional)
    pub reset: Option<SamplerResetFn>,
    /// Clone the sampler (optional if ctx is NULL)
    pub clone: Option<SamplerCloneFn>,
    /// Free the sampler (optional if ctx is NULL)
    pub free: Option<SamplerFreeFn>,
}

/// Converts from C llama_sampler_i struct reference to Rust SamplerInterface struct
///
/// Maps sampler interface function pointers:
/// - name: Function to get sampler name
/// - accept: Function to check if sampler accepts input
/// - apply: Required function to apply sampling logic
/// - reset: Function to reset sampler state
/// - clone: Function to clone sampler instance
/// - free: Function to free sampler resources
impl From<&llama_sampler_i> for SamplerInterface {
    fn from(iface: &llama_sampler_i) -> Self {
        Self {
            name: iface.name,
            accept: iface.accept,
            apply: iface.apply,  // Not an Option since it's required
            reset: iface.reset,
            clone: iface.clone,
            free: iface.free,
        }
    }
}


/// Converts from Rust SamplerInterface struct reference to C llama_sampler_i struct
///
/// Maps sampler interface function pointers back to C representation:
/// - name: Function to get sampler name
/// - accept: Function to check if sampler accepts input
/// - apply: Required function to apply sampling logic
/// - reset: Function to reset sampler state
/// - clone: Function to clone sampler instance
/// - free: Function to free sampler resources
impl From<&SamplerInterface> for llama_sampler_i {
    fn from(iface: &SamplerInterface) -> Self {
        Self {
            name: iface.name,
            accept: iface.accept,
            apply: iface.apply,  // Not an Option since it's required
            reset: iface.reset,
            clone: iface.clone,
            free: iface.free,
        }
    }
}

/// Safe wrapper around llama_sampler
pub struct SamplerImpl {
    /// Interface functions
    pub iface: SamplerInterface,
    /// Sampler context
    pub ctx: SamplerContext,
}

impl SamplerImpl {
    /// Initialize a new sampler with the given interface and context
    pub unsafe fn init(iface: SamplerInterface, ctx: SamplerContext) -> Option<Self> {
        // Ensure apply function is provided since it's required
        if iface.apply.is_none() {
            return None;
        }

        let c_iface = llama_sampler_i::from(iface.clone());
        let ptr = llama_sampler_init(&c_iface, ctx);
        if ptr.is_null() {
            None
        } else {
            Some(Self { iface, ctx })
        }
    }

    /// Get the sampler name
    pub fn name(&self) -> Option<String> {
        if let Some(name_fn) = self.iface.name {
            unsafe {
                let name = name_fn(self.as_ptr());
                if name.is_null() {
                    None
                } else {
                    Some(CStr::from_ptr(name).to_string_lossy().into_owned())
                }
            }
        } else {
            None
        }
    }

    /// Accept a token, updating internal state
    pub fn accept(&mut self, token: Token) {
        if let Some(accept_fn) = self.iface.accept {
            unsafe {
                accept_fn(self.as_ptr(), token);
            }
        }
    }

    /// Apply sampling to token probabilities
    pub fn apply(&mut self, candidates: &mut TokenDataArray) {
        if let Some(apply_fn) = self.iface.apply {
            unsafe {
                apply_fn(self.as_ptr(), candidates.as_mut_ptr());
            }
        }
    }

    /// Reset sampler state
    pub fn reset(&mut self) {
        if let Some(reset_fn) = self.iface.reset {
            unsafe {
                reset_fn(self.as_ptr());
            }
        }
    }

    /// Clone the sampler
    pub fn clone(&self) -> Option<Self> {
        if let Some(clone_fn) = self.iface.clone {
            unsafe {
                let ptr = clone_fn(self.as_ptr());
                if ptr.is_null() {
                    None
                } else {
                    Some(Self {
                        iface: self.iface.clone(),
                        ctx: (*ptr).ctx,
                    })
                }
            }
        } else {
            None
        }
    }

    /// Get the underlying pointer
    pub fn as_ptr(&self) -> *mut llama_sampler {
        &self.into_c_sampler() as *const _ as *mut _
    }

    /// Convert to C representation
    fn into_c_sampler(&self) -> llama_sampler {
        llama_sampler {
            iface: &llama_sampler_i::from(&self.iface) as *const _,
            ctx: self.ctx,
        }
    }
}

impl Drop for SamplerImpl {
    fn drop(&mut self) {
        if let Some(free_fn) = self.iface.free {
            unsafe {
                free_fn(self.as_ptr());
            }
        }
    }
}

/// Chain of samplers for token generation
pub struct SamplerChain {
    ptr: NonNull<llama_sampler>,
}

impl SamplerChain {
    /// Initialize a new sampler chain with the given parameters
    pub fn init(params: SamplerChainParams) -> Option<Self> {
        let ptr = unsafe {
            llama_sampler_chain_init(params.into())
        };
        NonNull::new(ptr).map(|ptr| Self { ptr })
    }

    /// Add a sampler to the chain
    /// Takes ownership of the sampler
    pub fn add(&mut self, sampler: SamplerImpl) {
        unsafe {
            llama_sampler_chain_add(self.ptr.as_ptr(), sampler.as_ptr());
        }
    }

    /// Get a sampler from the chain by index
    pub fn get(&self, i: i32) -> Option<&SamplerImpl> {
        unsafe {
            let ptr = llama_sampler_chain_get(self.ptr.as_ptr(), i);
            if ptr.is_null() {
                None
            } else {
                Some(&*(ptr as *const SamplerImpl))
            }
        }
    }

    /// Get the number of samplers in the chain
    pub fn len(&self) -> i32 {
        unsafe {
            llama_sampler_chain_n(self.ptr.as_ptr())
        }
    }

    /// Remove a sampler from the chain by index
    /// Returns the removed sampler if successful
    pub fn remove(&mut self, i: i32) -> Option<Box<SamplerImpl>> {
        unsafe {
            let ptr = llama_sampler_chain_remove(self.ptr.as_ptr(), i);
            if ptr.is_null() {
                None
            } else {
                Some(Box::from_raw(ptr as *mut SamplerImpl))
            }
        }
    }

    /// Sample a token from the logits
    pub fn sample(&mut self, ctx: &Context, idx: i32) -> Token {
        unsafe {
            llama_sampler_sample(self.ptr.as_ptr(), ctx.as_ptr(), idx)
        }
    }

    /// Get the underlying pointer
    pub fn as_ptr(&self) -> *mut llama_sampler {
        self.ptr.as_ptr()
    }

    /// Get performance data for sampler operations
    /// Note: Only works with samplers constructed via SamplerChain::init()
    pub fn perf_sampler(&self) -> PerfSamplerData {
        unsafe {
            llama_perf_sampler(self.ptr.as_ptr()).into()
        }
    }

    /// Print performance data for sampler operations
    /// Note: Only works with samplers constructed via SamplerChain::init()
    pub fn perf_sampler_print(&self) {
        unsafe {
            llama_perf_sampler_print(self.ptr.as_ptr());
        }
    }

    /// Reset performance counters for sampler operations
    /// Note: Only works with samplers constructed via SamplerChain::init()
    pub fn perf_sampler_reset(&mut self) {
        unsafe {
            llama_perf_sampler_reset(self.ptr.as_ptr());
        }
    }
}

/// Clean up sampler chain resources when dropped
///
/// Automatically frees the underlying C sampler chain and all its associated samplers.
/// For samplers that were added to the chain via `add()`, ownership is transferred to
/// the chain and they will be freed when the chain is dropped.
impl Drop for SamplerChain {
    fn drop(&mut self) {
        unsafe {
            llama_sampler_free(self.ptr.as_ptr());
        }
    }
}

// Sampler initialization functions

/// Initialize a greedy sampler that always selects the token with highest probability
pub fn sampler_init_greedy() -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_greedy();
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a sampler that samples from the probability distribution
pub fn sampler_init_dist(seed: u32) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_dist(seed);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a top-K sampler that only considers the K most likely tokens
pub fn sampler_init_top_k(k: i32) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_top_k(k);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a top-P (nucleus) sampler that considers tokens until cumulative probability exceeds P
pub fn sampler_init_top_p(p: f32, min_keep: usize) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_top_p(p, min_keep);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a min-P sampler that only considers tokens with probability >= P
pub fn sampler_init_min_p(p: f32, min_keep: usize) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_min_p(p, min_keep);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a temperature sampler that scales logits by 1/T
pub fn sampler_init_temp(t: f32) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_temp(t);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a dynamic temperature sampler
pub fn sampler_init_temp_ext(t: f32, delta: f32, exponent: f32) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_temp_ext(t, delta, exponent);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a Mirostat 1.0 sampler
///
/// Implements the Mirostat algorithm described in the paper https://arxiv.org/abs/2007.14966.
/// Uses tokens instead of words.
///
/// # Arguments
/// * `n_vocab` - Size of the vocabulary
/// * `seed` - Random seed for sampling
/// * `tau` - Target cross-entropy (surprise) value. Higher values = more surprising/less predictable text
/// * `eta` - Learning rate for updating mu. Higher values = faster updates
/// * `m` - Number of tokens for s_hat estimation (default=100 in paper)
pub fn sampler_init_mirostat(
    n_vocab: i32,
    seed: u32,
    tau: f32,
    eta: f32,
    m: i32
) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_mirostat(n_vocab, seed, tau, eta, m);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a Mirostat 2.0 sampler
///
/// Implements version 2.0 of the Mirostat algorithm from https://arxiv.org/abs/2007.14966.
/// Uses tokens instead of words.
///
/// # Arguments
/// * `seed` - Random seed for sampling
/// * `tau` - Target cross-entropy (surprise) value. Higher values = more surprising/less predictable text
/// * `eta` - Learning rate for updating mu. Higher values = faster updates
pub fn sampler_init_mirostat_v2(
    seed: u32,
    tau: f32,
    eta: f32
) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_mirostat_v2(seed, tau, eta);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a typical sampler that uses locally typical sampling
pub fn sampler_init_typical(p: f32, min_keep: usize) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_typical(p, min_keep);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize an XTC sampler
pub fn sampler_init_xtc(p: f32, t: f32, min_keep: usize, seed: u32) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_xtc(p, t, min_keep, seed);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a top-n-sigma sampler
pub fn sampler_init_top_n_sigma(n: f32) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_top_n_sigma(n);
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a grammar-based sampler
///
/// Creates a sampler that constrains token generation according to a grammar.
///
/// # Arguments
/// * `vocab` - The vocabulary to use for tokenization
/// * `grammar_str` - The grammar definition string
/// * `grammar_root` - The root rule of the grammar
pub fn sampler_init_grammar(
    vocab: &Vocab,
    grammar_str: &str,
    grammar_root: &str
) -> Option<SamplerImpl> {
    let grammar_str = CString::new(grammar_str).ok()?;
    let grammar_root = CString::new(grammar_root).ok()?;

    unsafe {
        let ptr = llama_sampler_init_grammar(
            vocab.as_ptr(),
            grammar_str.as_ptr(),
            grammar_root.as_ptr()
        );
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a lazy grammar sampler
///
/// Creates a grammar sampler that only activates when triggered by specific words/tokens.
///
/// # Arguments
/// * `vocab` - The vocabulary to use for tokenization
/// * `grammar_str` - The grammar definition string
/// * `grammar_root` - The root rule of the grammar
/// * `trigger_words` - List of words that will trigger the grammar sampler
/// * `trigger_tokens` - List of tokens that will trigger the grammar sampler
pub fn sampler_init_grammar_lazy(
    vocab: &Vocab,
    grammar_str: &str,
    grammar_root: &str,
    trigger_words: &[&str],
    trigger_tokens: &[Token],
) -> Option<SamplerImpl> {
    let grammar_str = CString::new(grammar_str).ok()?;
    let grammar_root = CString::new(grammar_root).ok()?;

    // Convert trigger words to C strings
    let trigger_words_c: Vec<CString> = trigger_words.iter()
        .map(|&s| CString::new(s))
        .collect::<Result<_, _>>()
        .ok()?;

    let mut trigger_word_ptrs: Vec<*const c_char> = trigger_words_c.iter()
        .map(|cs| cs.as_ptr())
        .collect();

    unsafe {
        let ptr = llama_sampler_init_grammar_lazy(
            vocab.as_ptr(),
            grammar_str.as_ptr(),
            grammar_root.as_ptr(),
            trigger_word_ptrs.as_mut_ptr() as *mut *const _,
            trigger_words.len(),
            trigger_tokens.as_ptr(),
            trigger_tokens.len()
        );
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}


/// Converts from C llama_sampler_i struct to Rust SamplerInterface struct
///
/// Maps sampler interface function pointers:
/// - name: Function to get sampler name
/// - accept: Function to check if sampler accepts input
/// - apply: Function to apply sampling logic
/// - reset: Function to reset sampler state
/// - clone: Function to clone sampler instance
/// - free: Function to free sampler resources
impl From<llama_sampler_i> for SamplerInterface {
    fn from(iface: llama_sampler_i) -> Self {
        Self {
            name: iface.name,
            accept: iface.accept,
            apply: iface.apply,
            reset: iface.reset,
            clone: iface.clone,
            free: iface.free,
        }
    }
}

/// Converts from Rust SamplerInterface struct to C llama_sampler_i struct
///
/// Maps sampler interface function pointers back to C representation:
/// - name: Function to get sampler name
/// - accept: Function to check if sampler accepts input
/// - apply: Function to apply sampling logic
/// - reset: Function to reset sampler state
/// - clone: Function to clone sampler instance
/// - free: Function to free sampler resources
impl From<SamplerInterface> for llama_sampler_i {
    fn from(iface: SamplerInterface) -> Self {
        Self {
            name: iface.name,
            accept: iface.accept,
            apply: iface.apply,
            reset: iface.reset,
            clone: iface.clone,
            free: iface.free,
        }
    }
}

/// Initialize a penalties sampler
///
/// NOTE: Avoid using on the full vocabulary as searching for repeated tokens can become slow.
/// For example, apply top-k or top-p sampling first.
///
/// # Arguments
/// * `penalty_last_n` - Last n tokens to penalize (0 = disable penalty, -1 = context size)
/// * `penalty_repeat` - Penalty for repeated tokens (1.0 = disabled)
/// * `penalty_freq` - Penalty for frequent tokens (0.0 = disabled)
/// * `penalty_present` - Penalty for tokens already in the context (0.0 = disabled)
pub fn sampler_init_penalties(
    penalty_last_n: i32,
    penalty_repeat: f32,
    penalty_freq: f32,
    penalty_present: f32
) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_penalties(
            penalty_last_n,
            penalty_repeat,
            penalty_freq,
            penalty_present
        );
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a DRY sampler
///
/// Implements the DRY (Don't Repeat Yourself) sampler designed by p-e-w.
/// As described in: https://github.com/oobabooga/text-generation-webui/pull/5677
/// Porting Koboldcpp implementation authored by pi6am: https://github.com/LostRuins/koboldcpp/pull/982
///
/// # Arguments
/// * `vocab` - The vocabulary to use for tokenization
/// * `n_ctx_train` - Training context size
/// * `dry_multiplier` - DRY multiplier
/// * `dry_base` - DRY base value
/// * `dry_allowed_length` - Maximum allowed length for repetition
/// * `dry_penalty_last_n` - Number of last tokens to check for repetition
/// * `seq_breakers` - List of sequence breaker strings
pub fn sampler_init_dry(
    vocab: &Vocab,
    n_ctx_train: i32,
    dry_multiplier: f32,
    dry_base: f32,
    dry_allowed_length: i32,
    dry_penalty_last_n: i32,
    seq_breakers: &[&str]
) -> Option<SamplerImpl> {
    // Convert sequence breakers to C strings
    let breakers_c: Vec<CString> = seq_breakers.iter()
        .map(|&s| CString::new(s))
        .collect::<Result<_, _>>()
        .ok()?;

    let mut breaker_ptrs: Vec<*const c_char> = breakers_c.iter()
        .map(|cs| cs.as_ptr())
        .collect();

    unsafe {
        let ptr = llama_sampler_init_dry(
            vocab.as_ptr(),
            n_ctx_train,
            dry_multiplier,
            dry_base,
            dry_allowed_length,
            dry_penalty_last_n,
            breaker_ptrs.as_mut_ptr() as *mut *const _,
            seq_breakers.len()
        );
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize a logit bias sampler
///
/// # Arguments
/// * `n_vocab` - Size of the vocabulary
/// * `logit_bias` - Array of token-specific biases to apply
pub fn sampler_init_logit_bias(
    n_vocab: i32,
    logit_bias: &[LogitBias]
) -> Option<SamplerImpl> {
    let c_biases: Vec<llama_logit_bias> = logit_bias.iter()
        .map(|bias| (*bias).into())
        .collect();

    unsafe {
        let ptr = llama_sampler_init_logit_bias(
            n_vocab,
            c_biases.len() as i32,
            c_biases.as_ptr()
        );
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Initialize an infill sampler for fill-in-the-middle tasks
///
/// This sampler is meant to be used after top_k + top_p sampling.
/// It performs the following operations:
/// 1. If the sum of EOG token probabilities times the number of candidates is higher than
///    the sum of other probabilities -> pick an EOG token
/// 2. Combine probabilities of tokens that have the same prefix
/// 3. Discard non-EOG tokens with low probability
/// 4. If no tokens remain -> pick EOT token
///
/// # Arguments
/// * `vocab` - The vocabulary to use for tokenization
pub fn sampler_init_infill(vocab: &Vocab) -> Option<SamplerImpl> {
    unsafe {
        let ptr = llama_sampler_init_infill(vocab.as_ptr());
        if ptr.is_null() {
            None
        } else {
            Some(SamplerImpl {
                iface: (*(*ptr).iface).into(),
                ctx: (*ptr).ctx,
            })
        }
    }
}

/// Get the seed used by the sampler if applicable
/// Returns LLAMA_DEFAULT_SEED if the sampler doesn't use a seed
pub fn sampler_get_seed(sampler: &SamplerImpl) -> u32 {
    unsafe {
        llama_sampler_get_seed(sampler.as_ptr())
    }
}

/// Build a split GGUF final path for this chunk
///
/// # Arguments
/// * `path_prefix` - The prefix path for the model (e.g. "/models/ggml-model-q4_0")
/// * `split_no` - The split number (0-based)
/// * `split_count` - Total number of splits
/// * `maxlen` - Maximum length of the output path buffer
///
/// # Returns
/// * `Ok(path)` - The constructed split path (e.g. "/models/ggml-model-q4_0-00002-of-00004.gguf")
/// * `Err(())` - If the buffer is too small or there was an error
pub fn split_path(path_prefix: &str, split_no: i32, split_count: i32, maxlen: usize) -> Result<String, ()> {
    let path_prefix = CString::new(path_prefix).map_err(|_| ())?;
    let mut buf = vec![0i8; maxlen];

    let result = unsafe {
        llama_split_path(
            buf.as_mut_ptr(),
            maxlen,
            path_prefix.as_ptr(),
            split_no,
            split_count
        )
    };

    if result > 0 {
        let c_str = unsafe { CStr::from_ptr(buf.as_ptr()) };
        Ok(c_str.to_string_lossy().into_owned())
    } else {
        Err(())
    }
}

/// Extract the path prefix from a split path
///
/// # Arguments
/// * `split_path` - The split path (e.g. "/models/ggml-model-q4_0-00002-of-00004.gguf")
/// * `split_no` - The split number (0-based)
/// * `split_count` - Total number of splits
/// * `maxlen` - Maximum length of the output prefix buffer
///
/// # Returns
/// * `Ok(prefix)` - The extracted prefix (e.g. "/models/ggml-model-q4_0")
/// * `Err(())` - If the buffer is too small, split numbers don't match, or there was an error
pub fn split_prefix(split_path: &str, split_no: i32, split_count: i32, maxlen: usize) -> Result<String, ()> {
    let split_path = CString::new(split_path).map_err(|_| ())?;
    let mut buf = vec![0i8; maxlen];

    let result = unsafe {
        llama_split_prefix(
            buf.as_mut_ptr(),
            maxlen,
            split_path.as_ptr(),
            split_no,
            split_count
        )
    };

    if result > 0 {
        let c_str = unsafe { CStr::from_ptr(buf.as_ptr()) };
        Ok(c_str.to_string_lossy().into_owned())
    } else {
        Err(())
    }
}

/// Get system information about available backends and their features
///
/// # Returns
/// A string containing information about the system's available backends and their features
pub fn print_system_info() -> String {
    unsafe {
        let ptr = llama_print_system_info();
        if ptr.is_null() {
            String::new()
        } else {
            CStr::from_ptr(ptr).to_string_lossy().into_owned()
        }
    }
}

/// Set callback for all future logging events
/// If callback is None, everything is output on stderr
///
/// # Arguments
/// * `callback` - The logging callback function
/// * `user_data` - User data pointer passed to the callback
pub fn log_set(callback: ggml_log_callback, user_data: Option<*mut std::ffi::c_void>) {
    unsafe {
        llama_log_set(
            callback,
            user_data.unwrap_or(std::ptr::null_mut())
        );
    }
}

/// Performance data for context operations
#[derive(Debug, Clone, Copy)]
pub struct PerfContextData {
    /// Start time in milliseconds
    pub t_start_ms: f64,
    /// Load time in milliseconds
    pub t_load_ms: f64,
    /// Prompt evaluation time in milliseconds
    pub t_p_eval_ms: f64,
    /// Evaluation time in milliseconds
    pub t_eval_ms: f64,
    /// Number of prompt evaluations
    pub n_p_eval: i32,
    /// Number of evaluations
    pub n_eval: i32,
}

/// Performance data for sampler operations
#[derive(Debug, Clone, Copy)]
pub struct PerfSamplerData {
    /// Sampling time in milliseconds
    pub t_sample_ms: f64,
    /// Number of samples
    pub n_sample: i32,
}

/// Converts from C llama_perf_context_data struct to Rust PerfContextData struct
///
/// Maps performance metrics for context evaluation:
/// - t_start_ms: Start time in milliseconds
/// - t_load_ms: Time spent loading model in milliseconds
/// - t_p_eval_ms: Time spent in prompt evaluation in milliseconds
/// - t_eval_ms: Total evaluation time in milliseconds
/// - n_p_eval: Number of prompt tokens evaluated
/// - n_eval: Total number of tokens evaluated
impl From<llama_perf_context_data> for PerfContextData {
    fn from(data: llama_perf_context_data) -> Self {
        Self {
            t_start_ms: data.t_start_ms,
            t_load_ms: data.t_load_ms,
            t_p_eval_ms: data.t_p_eval_ms,
            t_eval_ms: data.t_eval_ms,
            n_p_eval: data.n_p_eval,
            n_eval: data.n_eval,
        }
    }
}

/// Converts from C llama_perf_sampler_data struct to Rust PerfSamplerData struct
///
/// Maps performance metrics for token sampling:
/// - t_sample_ms: Time spent sampling in milliseconds
/// - n_sample: Number of tokens sampled
impl From<llama_perf_sampler_data> for PerfSamplerData {
    fn from(data: llama_perf_sampler_data) -> Self {
        Self {
            t_sample_ms: data.t_sample_ms,
            n_sample: data.n_sample,
        }
    }
}