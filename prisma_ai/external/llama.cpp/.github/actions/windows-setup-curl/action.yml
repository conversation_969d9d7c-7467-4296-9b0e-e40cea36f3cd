name: 'Windows - Setup CURL'
description: 'Composite action, to be reused in other workflow'
inputs:
  curl_version:
    description: 'CURL version'
    required: false
    default: '8.6.0_6'
  architecture:
    description: 'Architecture of the libcurl to download'
    required: false
    default: 'win64'
outputs:
  curl_path:
    description: "Path to the downloaded libcurl"
    value: ${{ steps.get_libcurl.outputs.curl_path }}

runs:
  using: "composite"
  steps:
    - name: libCURL
      id: get_libcurl
      shell: powershell
      env:
        CURL_VERSION: ${{ inputs.curl_version }}
        ARCHITECTURE: ${{ inputs.architecture }}
      run: |
        curl.exe -o $env:RUNNER_TEMP/curl.zip -L "https://curl.se/windows/dl-${env:CURL_VERSION}/curl-${env:CURL_VERSION}-${env:ARCHITECTURE}-mingw.zip"
        mkdir $env:RUNNER_TEMP/libcurl
        tar.exe -xvf $env:RUNNER_TEMP/curl.zip --strip-components=1 -C $env:RUNNER_TEMP/libcurl
        echo "curl_path=$env:RUNNER_TEMP/libcurl" >> $env:GITHUB_OUTPUT
