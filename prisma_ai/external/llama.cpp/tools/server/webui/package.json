{"name": "webui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run format && tsc -b && vite build", "format": "eslint . && prettier --write .", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@sec-ant/readable-stream": "^0.6.0", "@tailwindcss/postcss": "^4.1.1", "@tailwindcss/vite": "^4.1.1", "@vscode/markdown-it-katex": "^1.1.1", "autoprefixer": "^10.4.20", "daisyui": "^5.0.12", "dexie": "^4.0.11", "highlight.js": "^11.10.0", "katex": "^0.16.15", "pdfjs-dist": "^5.2.133", "postcss": "^8.4.49", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "react-markdown": "^9.0.3", "react-router": "^7.1.5", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "tailwindcss": "^4.1.1", "textlinestream": "^1.1.1", "vite-plugin-singlefile": "^2.0.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/markdown-it": "^14.1.2", "@types/node": "^22.13.1", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "fflate": "^0.8.2", "globals": "^15.14.0", "prettier": "^3.4.2", "sass-embedded": "^1.83.4", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}, "prettier": {"trailingComma": "es5", "tabWidth": 2, "semi": true, "singleQuote": true, "bracketSameLine": false}}