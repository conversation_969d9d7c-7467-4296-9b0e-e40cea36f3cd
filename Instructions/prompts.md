I have the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/generics.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/mod.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/prisma_engine.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/traits.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/types.rs module and I would you to analyze it and implement tests in the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/prisma_engine_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration_tests.rs ( entry point) files. the test will NOT use mocks or placeholders but real methods/components. the following tests to implement will be - 
#### Test: `test_long_running_stability`
**Purpose**: Test system stability over extended periods
**Components**: Full system, continuous operation
**Test Duration**: 24+ hours
**Monitoring**:
- Performance degradation
- Memory growth
- Error accumulation
- Resource exhaustion

Keep top-level imports minimal - Only types, no traits
Import traits locally - Within {} blocks where needed
Use helper functions - For creating monitors without trait dependencies
One trait per test block - Avoid mixing conflicting traits in same scope
Test-specific imports - Import only what each test actually uses
Don't replace current test/s, add new tests to the bottom of the file

NOTE : the llama API ( /Users/<USER>/Documents/prisma_workspace/prisma_ai/external/llama.cpp/include/llama.h ) has been updated, make sure it matched the rust code ( /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/llm/implementation
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/llm/interface ) to prevent any SIGSEGV or any errors and have a comprehensive test.

if needed -   # all Configurations can be found in - /Users/<USER>/Documents/prisma_workspace/configs/config.yaml


Follow the same pattern as the previous tests - /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/decision_maker_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/execution_strategies_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/executor_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/agent_manager_tests.rs
	





Do  a full analysis on the monitor module - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/prisma_engine.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/types.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/traits.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/mod.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/generics.rs -- and when completed, generate a comprhensive intergrated test plan in the /Users/<USER>/Documents/prisma_workspace/Instructions/tests.md. also have intergration tests for - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/generics.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/mod.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/prisma_engine.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/traits.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/types.rs -- with the other modules - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/agent_manager
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/decision_maker
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/execution_strategies
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/monitor
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/tcl
	




