# Comprehensive Integration Test Plan for Prisma Engine

## Overview

This document outlines a comprehensive integration test plan for the Prisma Engine, focusing on the monitor module and its integration with other core components. The plan emphasizes real implementations over mocks to ensure memory safety and prevent SIGSEGV crashes.

## Test Execution Guidelines

### Command Pattern
```bash
cargo test --lib --tests -- --nocapture --test-threads=1
```

### Key Principles
- **Real Implementations Only**: Use actual services, databases, and models
- **Single-threaded Execution**: Prevent race conditions and memory issues
- **Verbose Output**: Enable detailed debugging information
- **Memory Safety Focus**: Avoid mocks that can cause crashes

## Test Configuration

### Real Service Configurations
- **Database**: Use real SurrealDB from `configs/config.yaml`
- **LLM Model**: `/Users/<USER>/Documents/prisma_workspace/models/Meta-Llama-3-8B-Instruct.Q8_0.gguf`
- **Embedding Model**: `/Users/<USER>/Documents/prisma_workspace/models/nomic-embed-text-v2-moe.f32.gguf`
- **Storage**: Real storage components from `prisma_ai/src/storage`

## 1. Monitor Module Integration Tests

### 1.1 System Monitor Integration Tests

#### Test: `test_system_monitor_initialization`
**Purpose**: Verify system monitor initializes with real hardware monitoring
**Components**: SystemInfoMonitor, CPU/Memory/Disk/Network monitors
**Configuration**:
```rust
let config = MonitorConfig {
    poll_interval_ms: 1000,
};
let system_monitor = SystemInfoMonitor::new(config);
```
**Assertions**:
- Monitor initializes successfully
- All resource monitors are created
- Initial system score is calculated
- Background monitoring task starts

#### Test: `test_system_resource_monitoring_accuracy`
**Purpose**: Validate real system resource data collection
**Components**: All resource monitors
**Test Steps**:
1. Start system monitoring
2. Generate controlled system load
3. Verify metrics reflect actual system state
4. Check score calculation accuracy

#### Test: `test_system_monitor_lifecycle`
**Purpose**: Test start/stop/pause/resume operations
**Components**: SystemInfoMonitor lifecycle
**Test Steps**:
1. Initialize monitor
2. Start monitoring
3. Pause monitoring
4. Resume monitoring
5. Stop monitoring
6. Verify clean shutdown

### 1.2 Prisma Monitor Integration Tests

#### Test: `test_prisma_monitor_queue_tracking`
**Purpose**: Verify queue monitoring with real task queues
**Components**: QueueMonitor, TaskMonitor, real priority queues
**Test Steps**:
1. Initialize queue monitor
2. Create and enqueue real tasks
3. Monitor queue metrics
4. Verify accurate queue length tracking
5. Test queue processing rate calculation

#### Test: `test_prisma_monitor_task_lifecycle`
**Purpose**: Track task execution from creation to completion
**Components**: TaskMonitor, real task execution
**Test Steps**:
1. Create real LLM/embedding/storage tasks
2. Monitor task state transitions
3. Track execution times
4. Record success/failure rates
5. Verify metrics aggregation

### 1.3 Unified Monitor Integration Tests

#### Test: `test_monitor_score_aggregation`
**Purpose**: Test system + prisma score combination
**Components**: Monitor, SystemInfoMonitor, QueueMonitor, TaskMonitor
**Test Steps**:
1. Initialize full monitor stack
2. Generate system load
3. Create task queue pressure
4. Verify score aggregation logic
5. Test score-based decision influence

## 2. Decision Maker Integration Tests

### 2.1 Monitor Data Integration Tests

#### Test: `test_decision_maker_system_score_usage`
**Purpose**: Verify decision maker uses real monitor data
**Components**: RuleBasedDecisionMaker, Monitor, real system metrics
**Configuration**:
```rust
let decision_config = DecisionMakerConfig {
    high_cpu_threshold: 0.8,
    high_memory_threshold: 0.8,
    default_strategy: ExecutionStrategyType::Tokio,
    rule_evaluation_timeout_ms: 5000,
    enable_priority_adjustment: true,
    enable_rule_based_decisions: true,
    enable_state_tracking: true,
    category_strategies: HashMap::new(),
};
```

#### Test: `test_adaptive_strategy_selection`
**Purpose**: Test dynamic strategy selection based on real system state
**Components**: Decision maker, monitor, real resource constraints
**Test Scenarios**:
- High CPU load → Tokio strategy preference
- High memory usage → Direct execution preference
- Balanced resources → Rayon for CPU-intensive tasks
- Critical system state → Task rejection/queuing

### 2.2 Rule-Based Decision Integration Tests

#### Test: `test_complex_decision_scenarios`
**Purpose**: Test decision making under various real system conditions
**Components**: Full decision maker stack with real monitoring
**Test Cases**:
1. **Normal Load**: Verify optimal strategy selection
2. **High CPU**: Test CPU-aware routing
3. **Memory Pressure**: Test memory-conscious decisions
4. **I/O Bottleneck**: Test I/O-aware strategy selection
5. **Mixed Workload**: Test balanced decision making

## 3. Executor Integration Tests

### 3.1 Queue Management Integration Tests

#### Test: `test_executor_queue_monitoring_integration`
**Purpose**: Verify executor reports accurate queue metrics to monitor
**Components**: TaskExecutor, Monitor, real priority queues
**Test Steps**:
1. Initialize executor with monitoring
2. Submit tasks to different priority queues
3. Verify queue statistics reporting
4. Test queue overflow handling
5. Validate metrics accuracy

#### Test: `test_executor_task_routing_with_monitoring`
**Purpose**: Test task routing based on real monitor feedback
**Components**: TaskExecutor, Decision maker, Monitor
**Test Steps**:
1. Create tasks with different categories/priorities
2. Monitor system state changes
3. Verify routing decisions adapt to system state
4. Test routing under resource constraints

### 3.2 Performance Reporting Integration Tests

#### Test: `test_executor_performance_feedback_loop`
**Purpose**: Test executor → monitor → decision maker feedback loop
**Components**: Full execution stack with real monitoring
**Test Steps**:
1. Execute tasks with performance monitoring
2. Verify metrics flow to monitor
3. Test decision maker adaptation
4. Validate performance improvements over time

## 4. Cross-Module Integration Tests

### 4.1 End-to-End Task Flow Tests

#### Test: `test_complete_task_lifecycle_with_monitoring`
**Purpose**: Test full task flow with comprehensive monitoring
**Components**: TCL, Decision Maker, Executor, Monitor, real services
**Test Flow**:
```
TCL Task Creation → Decision Making → Executor Routing →
Task Execution → Performance Monitoring → Metrics Aggregation
```

#### Test: `test_llm_task_integration_with_monitoring`
**Purpose**: Test LLM task execution with real model and monitoring
**Components**: LLM service, real model, full monitoring stack
**Configuration**:
- Use real LLM model at specified path
- Monitor GPU/CPU usage during inference
- Track inference latency and throughput
- Test error handling and recovery

#### Test: `test_embedding_task_integration_with_monitoring`
**Purpose**: Test embedding generation with real model and monitoring
**Components**: Embedding service, real model, monitoring
**Test Steps**:
1. Create embedding tasks with real text
2. Monitor resource usage during generation
3. Track generation performance
4. Test different pooling strategies
5. Verify normalization options

#### Test: `test_storage_task_integration_with_monitoring`
**Purpose**: Test database operations with real SurrealDB and monitoring
**Components**: Storage service, real SurrealDB, monitoring
**Test Steps**:
1. Initialize real SurrealDB connection
2. Execute database operations
3. Monitor connection pool usage
4. Track query performance
5. Test transaction monitoring

### 4.2 Agent Manager Integration Tests

#### Test: `test_agent_manager_monitoring_integration`
**Purpose**: Test agent lifecycle monitoring
**Components**: AgentManager, Monitor, real agent operations
**Test Steps**:
1. Create agents with monitoring
2. Track agent state transitions
3. Monitor agent resource usage
4. Test agent communication monitoring
5. Verify agent performance metrics

### 4.3 Service Integration Tests

#### Test: `test_service_health_monitoring`
**Purpose**: Monitor health of all integrated services
**Components**: All services, comprehensive monitoring
**Services to Monitor**:
- LLM service availability and performance
- Embedding service health
- Database connection status
- Agent manager service state
- Queue service health

## 5. Error Handling and Recovery Tests

### 5.1 Monitor Error Handling Tests

#### Test: `test_monitor_service_failure_recovery`
**Purpose**: Test monitor behavior when services fail
**Components**: Monitor, simulated service failures
**Test Scenarios**:
- System monitor failure → Graceful degradation
- Queue monitor failure → Fallback metrics
- Task monitor failure → Basic tracking continuation

### 5.2 Decision Maker Error Handling Tests

#### Test: `test_decision_maker_monitor_unavailable`
**Purpose**: Test decision making when monitor data is unavailable
**Components**: Decision maker, monitor failure simulation
**Test Steps**:
1. Start with working monitor
2. Simulate monitor failure
3. Verify fallback decision strategies
4. Test recovery when monitor returns

### 5.3 Executor Error Handling Tests

#### Test: `test_executor_monitoring_failure_handling`
**Purpose**: Test executor behavior when monitoring fails
**Components**: Executor, monitor failure scenarios
**Test Cases**:
- Queue monitoring failure → Continue execution
- Performance monitoring failure → Basic metrics
- Decision maker unavailable → Default strategies

## 6. Performance and Scalability Tests

### 6.1 Monitor Performance Tests

#### Test: `test_monitor_performance_under_load`
**Purpose**: Test monitoring performance with high task volumes
**Components**: Full monitoring stack, high task load
**Metrics to Track**:
- Monitoring overhead percentage
- Metrics collection latency
- Memory usage growth
- CPU usage for monitoring

### 6.2 Integration Performance Tests

#### Test: `test_end_to_end_performance_with_monitoring`
**Purpose**: Measure performance impact of comprehensive monitoring
**Components**: Full system with and without monitoring
**Comparisons**:
- Task throughput with/without monitoring
- Resource usage overhead
- Decision making latency impact
- Overall system performance

## 7. Configuration and Initialization Tests

### 7.1 Configuration Integration Tests

#### Test: `test_integrated_configuration_loading`
**Purpose**: Test configuration loading across all modules
**Components**: All modules, real configuration files
**Test Steps**:
1. Load configuration from `configs/config.yaml`
2. Initialize all modules with configuration
3. Verify configuration propagation
4. Test configuration validation
5. Test configuration updates

### 7.2 Service Initialization Tests

#### Test: `test_service_initialization_order`
**Purpose**: Test proper service initialization sequence
**Components**: All services, dependency management
**Test Steps**:
1. Initialize services in correct order
2. Verify dependency satisfaction
3. Test initialization failure recovery
4. Validate service health checks

## 8. Memory Safety and Stability Tests

### 8.1 Memory Safety Tests

#### Test: `test_memory_safety_under_load`
**Purpose**: Ensure no memory leaks or crashes under load
**Components**: Full system, extended load testing
**Test Duration**: Extended runs (30+ minutes)
**Monitoring**:
- Memory usage patterns
- Handle leaks
- Thread safety
- Resource cleanup

### 8.2 Stability Tests

#### Test: `test_long_running_stability`
**Purpose**: Test system stability over extended periods
**Components**: Full system, continuous operation
**Test Duration**: 24+ hours
**Monitoring**:
- Performance degradation
- Memory growth
- Error accumulation
- Resource exhaustion

## 9. Test Implementation Guidelines

### 9.1 Test File Organization

#### Monitor Tests
- `tests/integration/monitor_integration_tests.rs`
- `tests/integration/system_monitor_tests.rs`
- `tests/integration/prisma_monitor_tests.rs`

#### Cross-Module Integration Tests
- `tests/integration/engine_integration_tests.rs`
- `tests/integration/task_flow_integration_tests.rs`
- `tests/integration/service_integration_tests.rs`

### 9.2 Test Helper Functions

#### Configuration Helpers
```rust
fn create_real_engine_config() -> EngineConfig {
    // Load from real config file
    // Initialize with real service paths
}

fn create_real_monitor_config() -> MonitorConfig {
    // Real monitoring configuration
}

fn setup_real_services() -> (LlmService, EmbeddingService, StorageService) {
    // Initialize real services
}
```

#### Test Utilities
```rust
fn wait_for_system_stabilization() {
    // Wait for system metrics to stabilize
}

fn generate_controlled_load() {
    // Create predictable system load
}

fn verify_metrics_accuracy(expected: &SystemMetrics, actual: &SystemMetrics) {
    // Validate metrics within acceptable ranges
}
```

### 9.3 Test Data Management

#### Real Test Data
- Use actual text for embedding tests
- Use realistic prompts for LLM tests
- Use representative database queries
- Use actual system workloads

#### Test Cleanup
- Ensure proper resource cleanup
- Reset system state between tests
- Clean up temporary files and connections
- Restore original configurations

## 10. Continuous Integration Considerations

### 10.1 CI Test Categories

#### Fast Integration Tests (< 5 minutes)
- Basic initialization tests
- Simple integration flows
- Configuration validation

#### Medium Integration Tests (5-15 minutes)
- Complex integration scenarios
- Performance validation
- Error handling tests

#### Long Integration Tests (15+ minutes)
- Stability tests
- Extended load tests
- Memory safety validation

### 10.2 Test Environment Requirements

#### Hardware Requirements
- Sufficient RAM for real model loading
- CPU cores for parallel testing
- Disk space for model files and databases

#### Software Requirements
- Real database instance
- Model files available
- Network connectivity for services

## 11. Success Criteria

### 11.1 Integration Test Success Metrics

#### Functional Success
- All integration flows complete successfully
- Real services integrate without crashes
- Monitoring data flows correctly between modules
- Decision making adapts to real system conditions

#### Performance Success
- Monitoring overhead < 5% of total system resources
- Decision making latency < 100ms
- Task routing efficiency > 95%
- No memory leaks over 24-hour runs

#### Stability Success
- Zero SIGSEGV crashes during test runs
- Graceful handling of all error conditions
- Successful recovery from service failures
- Consistent performance over extended periods

### 11.2 Quality Gates

#### Before Merge
- All integration tests pass
- No memory safety issues detected
- Performance benchmarks met
- Error handling validated

#### Before Release
- Extended stability tests pass
- Real-world load testing successful
- Documentation updated
- Monitoring dashboards functional

This comprehensive test plan ensures thorough validation of the monitor module and its integration with all other Prisma Engine components, emphasizing real implementations and memory safety as per user requirements.