[{"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [0]", "scheme": "rust-analyzer-diagnostics-view", "query": "0", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 1885, "startColumn": 40, "endLineNumber": 1885, "endColumn": 65}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 1885, "startColumn": 40, "endLineNumber": 1885, "endColumn": 65}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [1]", "scheme": "rust-analyzer-diagnostics-view", "query": "1", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 2161, "startColumn": 42, "endLineNumber": 2161, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 2161, "startColumn": 42, "endLineNumber": 2161, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [2]", "scheme": "rust-analyzer-diagnostics-view", "query": "2", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 4999, "startColumn": 41, "endLineNumber": 4999, "endColumn": 66}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 4999, "startColumn": 41, "endLineNumber": 4999, "endColumn": 66}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [3]", "scheme": "rust-analyzer-diagnostics-view", "query": "3", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5019, "startColumn": 44, "endLineNumber": 5019, "endColumn": 69}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5019, "startColumn": 44, "endLineNumber": 5019, "endColumn": 69}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [4]", "scheme": "rust-analyzer-diagnostics-view", "query": "4", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5040, "startColumn": 40, "endLineNumber": 5040, "endColumn": 65}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5040, "startColumn": 40, "endLineNumber": 5040, "endColumn": 65}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [5]", "scheme": "rust-analyzer-diagnostics-view", "query": "5", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5084, "startColumn": 44, "endLineNumber": 5084, "endColumn": 69}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5084, "startColumn": 44, "endLineNumber": 5084, "endColumn": 69}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [6]", "scheme": "rust-analyzer-diagnostics-view", "query": "6", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5121, "startColumn": 40, "endLineNumber": 5121, "endColumn": 65}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5121, "startColumn": 40, "endLineNumber": 5121, "endColumn": 65}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [7]", "scheme": "rust-analyzer-diagnostics-view", "query": "7", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5159, "startColumn": 35, "endLineNumber": 5159, "endColumn": 60}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5159, "startColumn": 35, "endLineNumber": 5159, "endColumn": 60}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [8]", "scheme": "rust-analyzer-diagnostics-view", "query": "8", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5219, "startColumn": 41, "endLineNumber": 5219, "endColumn": 66}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5219, "startColumn": 41, "endLineNumber": 5219, "endColumn": 66}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [9]", "scheme": "rust-analyzer-diagnostics-view", "query": "9", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5235, "startColumn": 42, "endLineNumber": 5235, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5235, "startColumn": 42, "endLineNumber": 5235, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [10]", "scheme": "rust-analyzer-diagnostics-view", "query": "10", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5253, "startColumn": 40, "endLineNumber": 5253, "endColumn": 65}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5253, "startColumn": 40, "endLineNumber": 5253, "endColumn": 65}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [11]", "scheme": "rust-analyzer-diagnostics-view", "query": "11", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5271, "startColumn": 42, "endLineNumber": 5271, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5271, "startColumn": 42, "endLineNumber": 5271, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [12]", "scheme": "rust-analyzer-diagnostics-view", "query": "12", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5293, "startColumn": 43, "endLineNumber": 5293, "endColumn": 68}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5293, "startColumn": 43, "endLineNumber": 5293, "endColumn": 68}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [13]", "scheme": "rust-analyzer-diagnostics-view", "query": "13", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5314, "startColumn": 42, "endLineNumber": 5314, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5314, "startColumn": 42, "endLineNumber": 5314, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [14]", "scheme": "rust-analyzer-diagnostics-view", "query": "14", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5363, "startColumn": 43, "endLineNumber": 5363, "endColumn": 68}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5363, "startColumn": 43, "endLineNumber": 5363, "endColumn": 68}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [15]", "scheme": "rust-analyzer-diagnostics-view", "query": "15", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5382, "startColumn": 42, "endLineNumber": 5382, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5382, "startColumn": 42, "endLineNumber": 5382, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [16]", "scheme": "rust-analyzer-diagnostics-view", "query": "16", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5401, "startColumn": 42, "endLineNumber": 5401, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5401, "startColumn": 42, "endLineNumber": 5401, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [17]", "scheme": "rust-analyzer-diagnostics-view", "query": "17", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5439, "startColumn": 45, "endLineNumber": 5439, "endColumn": 70}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5439, "startColumn": 45, "endLineNumber": 5439, "endColumn": 70}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [18]", "scheme": "rust-analyzer-diagnostics-view", "query": "18", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5465, "startColumn": 43, "endLineNumber": 5465, "endColumn": 68}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5465, "startColumn": 43, "endLineNumber": 5465, "endColumn": 68}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [19]", "scheme": "rust-analyzer-diagnostics-view", "query": "19", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5486, "startColumn": 44, "endLineNumber": 5486, "endColumn": 69}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5486, "startColumn": 44, "endLineNumber": 5486, "endColumn": 69}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [20]", "scheme": "rust-analyzer-diagnostics-view", "query": "20", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5528, "startColumn": 42, "endLineNumber": 5528, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5528, "startColumn": 42, "endLineNumber": 5528, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [21]", "scheme": "rust-analyzer-diagnostics-view", "query": "21", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5545, "startColumn": 42, "endLineNumber": 5545, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5545, "startColumn": 42, "endLineNumber": 5545, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [22]", "scheme": "rust-analyzer-diagnostics-view", "query": "22", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5562, "startColumn": 49, "endLineNumber": 5562, "endColumn": 74}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5562, "startColumn": 49, "endLineNumber": 5562, "endColumn": 74}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [23]", "scheme": "rust-analyzer-diagnostics-view", "query": "23", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5579, "startColumn": 42, "endLineNumber": 5579, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5579, "startColumn": 42, "endLineNumber": 5579, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [24]", "scheme": "rust-analyzer-diagnostics-view", "query": "24", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5605, "startColumn": 42, "endLineNumber": 5605, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5605, "startColumn": 42, "endLineNumber": 5605, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [25]", "scheme": "rust-analyzer-diagnostics-view", "query": "25", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5623, "startColumn": 40, "endLineNumber": 5623, "endColumn": 65}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5623, "startColumn": 40, "endLineNumber": 5623, "endColumn": 65}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [26]", "scheme": "rust-analyzer-diagnostics-view", "query": "26", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5640, "startColumn": 46, "endLineNumber": 5640, "endColumn": 71}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5640, "startColumn": 46, "endLineNumber": 5640, "endColumn": 71}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rustc", "code": {"value": "Click for full compiler diagnostic", "target": {"$mid": 1, "path": "/diagnostic message [27]", "scheme": "rust-analyzer-diagnostics-view", "query": "27", "fragment": "file:///Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs"}}, "severity": 8, "message": "missing field `normalization` in initializer of `EmbeddingGenerationParams`\nmissing `normalization`", "source": "rustc", "startLineNumber": 5657, "startColumn": 42, "endLineNumber": 5657, "endColumn": 67}, {"resource": "/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/tcl_tests.rs", "owner": "rust-analyzer", "code": {"value": "E0063", "target": {"$mid": 1, "path": "/stable/error_codes/E0063.html", "scheme": "https", "authority": "doc.rust-lang.org"}}, "severity": 8, "message": "missing structure fields:\n- normalization\n", "source": "rust-analyzer", "startLineNumber": 5657, "startColumn": 42, "endLineNumber": 5657, "endColumn": 67}]