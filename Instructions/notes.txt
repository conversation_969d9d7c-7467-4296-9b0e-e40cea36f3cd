You can access:
RabbitMQ management interface at http://localhost:15672
SurrealDB is accessible at http://localhost:8000
Loki is ready to receive logs at http://localhost:3100


grafna service token - glsa_Go16KlrwTtqtNmeymEg56hIysMVEHyVg_8be567e3
Name :
prisma-pipeline	
ID :
sa-1-prisma-pipeline

#pull
git fetch origin
git reset --hard origin/main
git clean -fd

# Run all tests (unit + integration) with verbose output
cargo test -- --nocapture

# Or with even more verbose output showing test names as they run
cargo test -- --nocapture --test-threads=1
cargo test --lib --tests -- --nocapture --test-threads=1

# push at the push command
git push --set-upstream origin main:main

# surrealdb url test
curl -v http://localhost:8000/status

# tests
cargo test --lib --tests tcl_tests -- --nocapture --test-threads=1

#############################################################################################################

flutter run -d macos
flutter run -d "iPhone 12" ( open -a Simulator )
flutter run -d "iPhone 12" --release
flutter run -d macos --release

flutter devices

#############################################################################################################

I have the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/generics.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/mod.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/prisma_engine.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/traits.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/types.rs module and I would you to analyze it and implement tests in the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/prisma_engine_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration_tests.rs ( entry point) files. the test will NOT use mocks or placeholders but real methods/components. the following tests to implement will be - 
#### Test: `test_executor_task_routing_with_monitoring`
**Purpose**: Test task routing based on real monitor feedback
**Components**: TaskExecutor, Decision maker, Monitor
**Test Steps**:
1. Create tasks with different categories/priorities
2. Monitor system state changes
3. Verify routing decisions adapt to system state
4. Test routing under resource constraints

Keep top-level imports minimal - Only types, no traits
Import traits locally - Within {} blocks where needed
Use helper functions - For creating monitors without trait dependencies
One trait per test block - Avoid mixing conflicting traits in same scope
Test-specific imports - Import only what each test actually uses
Don't replace current test/s, add new tests to the bottom of the file

NOTE : the llama API ( /Users/<USER>/Documents/prisma_workspace/prisma_ai/external/llama.cpp/include/llama.h ) has been updated, make sure it matched the rust code ( /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/llm/implementation
/Users/<USER>/Documents/prisma_workspace/prisma_ai/src/llm/interface ) to prevent any SIGSEGV or any errors and have a comprehensive test.


if needed -   # all Configurations can be found in - /Users/<USER>/Documents/prisma_workspace/configs/config.yaml
  