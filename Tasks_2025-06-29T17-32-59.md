[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Add normalization parameter to EmbeddingGenerationParams DESCRIPTION:Update the EmbeddingGenerationParams structure in prisma_ai/src/prisma/prisma_engine/tcl/types.rs to include a normalization field that supports the llama.cpp normalization options (-1=none, 0=max absolute, 1=taxicab, 2=euclidean, >2=p-norm)
-[x] NAME:Update EmbeddingGenerator trait interface DESCRIPTION:Modify the EmbeddingGenerator trait in prisma_ai/src/llm/interface/embedding.rs to accept normalization parameters in the generate_embeddings method
-[x] NAME:Add FFI binding for common_embd_normalize DESCRIPTION:Add FFI binding for the common_embd_normalize function from llama.cpp to enable normalization functionality in Rust code
-[x] NAME:Update Context implementation DESCRIPTION:Modify the Context implementation in prisma_ai/src/llm/implementation/context_impl.rs to use normalization parameters when generating embeddings
-[x] NAME:Update service implementations DESCRIPTION:Update LlmServiceImpl and EmbeddingServiceImpl to pass through normalization parameters to the underlying Context implementation
-[x] NAME:Update embedding task execution DESCRIPTION:Modify the EmbeddingTask execution in prisma_ai/src/prisma/prisma_engine/tcl/embedding_task.rs to pass normalization parameters from EmbeddingGenerationParams to the embedding service